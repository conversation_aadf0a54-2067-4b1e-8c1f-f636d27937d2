//
//  ManualInputView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct ManualInputView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var dataManager: ExpenseDataManager
    @State private var amount: String = ""
    @State private var selectedCategory = "Food & Dining"
    @State private var description: String = ""
    @State private var selectedDate = Date()

    let categories = [
        ("Food & Dining", "🍽️", Color.orange),
        ("Transportation", "🚗", Color.blue),
        ("Shopping", "🛍️", Color.green),
        ("Entertainment", "🎬", Color.purple),
        ("Healthcare", "🏥", Color.red),
        ("Others", "📝", Color.gray)
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Amount Input
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Amount")
                            .font(.headline)
                            .fontWeight(.semibold)

                        HStack {
                            Text(dataManager.getCurrencySymbol())
                                .font(.title2)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)

                            TextField("0.00", text: $amount)
                                .font(.title2)
                                .keyboardType(.decimalPad)
                                .textFieldStyle(PlainTextFieldStyle())
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }

                    // Category Selection
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Category")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                            ForEach(categories, id: \.0) { category in
                                CategoryButton(
                                    name: category.0,
                                    icon: category.1,
                                    color: category.2,
                                    isSelected: selectedCategory == category.0
                                ) {
                                    selectedCategory = category.0
                                }
                            }
                        }
                    }
                    
                    // Description Input
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Description")
                            .font(.headline)
                            .fontWeight(.semibold)

                        TextField("Add description", text: $description)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                    }

                    // Date Selection
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Date")
                            .font(.headline)
                            .fontWeight(.semibold)

                        DatePicker("Select date", selection: $selectedDate, displayedComponents: [.date])
                            .datePickerStyle(CompactDatePickerStyle())
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(12)
                    }
                    
                    Spacer(minLength: 40)
                    
                    // Save Button
                    Button(action: saveRecord) {
                        Text("Save Record")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.blue, Color.purple]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(12)
                    }
                    .disabled(amount.isEmpty)
                    .opacity(amount.isEmpty ? 0.6 : 1.0)
                }
                .padding()
            }
            .navigationTitle("Manual Entry")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveRecord()
                    }
                    .disabled(amount.isEmpty)
                }
            }
        }
    }
    
    private func saveRecord() {
        guard let amountValue = Double(amount), amountValue > 0 else {
            return
        }

        let newRecord = ExpenseRecord(
            id: UUID().uuidString,
            category: selectedCategory,
            amount: amountValue,
            description: description.isEmpty ? selectedCategory : description,
            time: DateFormatter.timeFormatter.string(from: selectedDate),
            icon: categories.first { $0.0 == selectedCategory }?.1 ?? "📝",
            color: categories.first { $0.0 == selectedCategory }?.2 ?? .gray,
            date: selectedDate
        )

        // Save the record using data manager
        dataManager.addExpense(newRecord)

        presentationMode.wrappedValue.dismiss()
    }
}

struct CategoryButton: View {
    let name: String
    let icon: String
    let color: Color
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(icon)
                    .font(.system(size: 24))
                
                Text(name)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 70)
            .background(
                isSelected ? color : Color(.systemGray6)
            )
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(color, lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

extension DateFormatter {
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter
    }()
}

#Preview {
    ManualInputView()
}

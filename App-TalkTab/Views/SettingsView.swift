//
//  SettingsView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var dataManager: NetworkDataManager
    @State private var showingResetAlert = false
    @State private var showingAboutAlert = false
    @State private var showingExportSheet = false
    @State private var exportedCSV = ""

    let currencies = ["USD", "EUR", "GBP", "JPY", "SGD", "MYR", "THB"]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    HStack {
                        Text("Settings")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        Spacer()
                    }
                    .padding(.horizontal)

                    // Currency Settings
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Image(systemName: "dollarsign.circle.fill")
                                .font(.title2)
                                .foregroundColor(.green)

                            Text("Currency Settings")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Spacer()
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Default Currency")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Picker("Currency", selection: $dataManager.selectedCurrency) {
                                ForEach(currencies, id: \.self) { currency in
                                    HStack {
                                        Text(getCurrencySymbol(currency))
                                        Text(currency)
                                    }
                                    .tag(currency)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(16)
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                    .padding(.horizontal)
                    
                    // Category Management
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Image(systemName: "folder.fill")
                                .font(.title2)
                                .foregroundColor(.blue)

                            Text("Category Management")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Spacer()

                            Button(action: {
                                // TODO: Add new category
                            }) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.blue)
                            }
                        }
                        
                        LazyVStack(spacing: 12) {
                            ForEach(dataManager.categories) { category in
                                CategorySettingRowView(category: category) {
                                    // TODO: Edit category
                                } onDelete: {
                                    // TODO: Delete category
                                    if let index = dataManager.categories.firstIndex(where: { $0.id == category.id }) {
                                        dataManager.categories.remove(at: index)
                                    }
                                }
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(16)
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                    .padding(.horizontal)
                    
                    // Other Settings
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Image(systemName: "gearshape.fill")
                                .font(.title2)
                                .foregroundColor(.gray)

                            Text("Other Settings")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Spacer()
                        }

                        VStack(spacing: 12) {
                            SettingRowView(
                                icon: "icloud.fill",
                                title: "Data Sync",
                                subtitle: "iCloud Sync",
                                iconColor: .blue
                            ) {
                                // TODO: Data sync settings
                            }

                            SettingRowView(
                                icon: "square.and.arrow.up.fill",
                                title: "Export Data",
                                subtitle: "Export as CSV file",
                                iconColor: .green
                            ) {
                                exportData()
                            }

                            SettingRowView(
                                icon: "arrow.triangle.2.circlepath",
                                title: "Test Migration",
                                subtitle: "Create test data & migrate",
                                iconColor: .purple
                            ) {
                                testDataMigration()
                            }

                            SettingRowView(
                                icon: "trash.fill",
                                title: "Reset Data",
                                subtitle: "Clear all expenses",
                                iconColor: .red
                            ) {
                                showingResetAlert = true
                            }

                            SettingRowView(
                                icon: "info.circle.fill",
                                title: "About App",
                                subtitle: "Version 1.0.0",
                                iconColor: .orange
                            ) {
                                showingAboutAlert = true
                            }
                        }
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(16)
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                    .padding(.horizontal)
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
        }
        .alert("Reset All Data", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                dataManager.resetToSampleData()
            }
        } message: {
            Text("This will permanently delete all your expense records and reset to sample data. This action cannot be undone.")
        }
        .alert("About Voice Accounting", isPresented: $showingAboutAlert) {
            Button("OK") { }
        } message: {
            Text("Voice Accounting v1.0.0\n\nA modern expense tracking app with voice input capabilities.\n\nData is stored locally on your device using UserDefaults.")
        }
        .sheet(isPresented: $showingExportSheet) {
            NavigationView {
                ScrollView {
                    Text(exportedCSV)
                        .font(.system(.caption, design: .monospaced))
                        .padding()
                }
                .navigationTitle("Exported Data")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            showingExportSheet = false
                        }
                    }
                }
            }
        }
    }

    private func exportData() {
        exportedCSV = dataManager.exportDataAsCSV()
        showingExportSheet = true
    }

    private func testDataMigration() {
        // Create test local data and restart the app to see migration in action
        dataManager.createTestLocalData()

        // Show an alert to inform the user
        let alert = UIAlertController(
            title: "Test Data Created",
            message: "Test local data has been created in the old format. Please restart the app to see the migration in action. Check the console for migration logs.",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(alert, animated: true)
        }
    }
    
    private func getCurrencySymbol(_ currency: String) -> String {
        switch currency {
        case "USD": return "$"
        case "EUR": return "€"
        case "GBP": return "£"
        case "JPY": return "¥"
        case "SGD": return "S$"
        case "MYR": return "RM"
        case "THB": return "฿"
        default: return currency
        }
    }
}

struct CategorySettingRowView: View {
    let category: ExpenseCategory
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(category.icon)
                .font(.system(size: 20))
                .frame(width: 40, height: 40)
                .background(category.color.opacity(0.2))
                .cornerRadius(8)
            
            // Category Details
            VStack(alignment: .leading, spacing: 2) {
                Text(category.name)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text("\(category.subcategories.count) subcategories")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 8) {
                Button(action: onEdit) {
                    Image(systemName: "pencil")
                        .font(.system(size: 14))
                        .foregroundColor(.blue)
                        .frame(width: 28, height: 28)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(6)
                }
                
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .font(.system(size: 14))
                        .foregroundColor(.red)
                        .frame(width: 28, height: 28)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(6)
                }
            }
        }
        .padding(.vertical, 8)
    }
}

struct SettingRowView: View {
    let icon: String
    let title: String
    let subtitle: String
    let iconColor: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 18))
                    .foregroundColor(iconColor)
                    .frame(width: 32, height: 32)
                    .background(iconColor.opacity(0.1))
                    .cornerRadius(8)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    SettingsView()
}

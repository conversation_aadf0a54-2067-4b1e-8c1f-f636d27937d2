//
//  HomeView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct HomeView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    @State private var monthlyChange: Double = 12.5
    @State private var isRecording = false
    @State private var showingManualInput = false

    private var todayRecords: [ExpenseRecord] {
        let calendar = Calendar.current
        let today = Date()
        return dataManager.expenses.filter { calendar.isDate($0.date, equalTo: today, toGranularity: .day) }
    }

    private var totalAmount: Double {
        dataManager.getTotalSpending(for: .monthly)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    HStack {
                        Text("Home")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        Spacer()
                    }
                    .padding(.horizontal)

                    // Total Amount Card
                    VStack(spacing: 8) {
                        Text("Monthly Total Spending")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("\(dataManager.getCurrencySymbol()) \(String(format: "%.2f", totalAmount))")
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(.primary)

                        HStack {
                            Text("vs last month")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("+\(String(format: "%.1f", monthlyChange))%")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.vertical, 20)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(16)
                    .padding(.horizontal)
                    
                    // Action Buttons
                    HStack(spacing: 16) {
                        // Voice Recording Button
                        Button(action: {
                            startVoiceRecording()
                        }) {
                            VStack(spacing: 8) {
                                Image(systemName: "mic.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                                Text("Voice Recording")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 80)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.purple, Color.blue]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(16)
                            .scaleEffect(isRecording ? 1.1 : 1.0)
                            .animation(.easeInOut(duration: 0.1), value: isRecording)
                        }
                        
                        // Manual Input Button
                        Button(action: {
                            showingManualInput = true
                        }) {
                            VStack(spacing: 8) {
                                Image(systemName: "pencil")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                                Text("Manual Entry")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 80)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.pink, Color.orange]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(16)
                        }
                    }
                    .padding(.horizontal)
                    
                    // Today's Records
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text("Today's Records")
                                .font(.headline)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        .padding(.horizontal)
                        
                        LazyVStack(spacing: 12) {
                            ForEach(todayRecords) { record in
                                RecordRowView(record: record)
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingManualInput) {
            ManualInputView()
                .environmentObject(dataManager)
        }
    }
    
    private func startVoiceRecording() {
        isRecording = true
        // TODO: Implement voice recording logic
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            isRecording = false
        }
    }
}

struct RecordRowView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    let record: ExpenseRecord

    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(record.icon)
                .font(.system(size: 20))
                .frame(width: 40, height: 40)
                .background(record.color.opacity(0.2))
                .cornerRadius(8)
            
            // Record Details
            VStack(alignment: .leading, spacing: 2) {
                Text(record.category)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(record.time)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Amount
            Text("\(dataManager.getCurrencySymbol())\(String(format: "%.2f", record.amount))")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    HomeView()
        .environmentObject(NetworkDataManager())
}

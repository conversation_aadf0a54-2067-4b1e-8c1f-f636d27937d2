//
//  HomeView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct HomeView: View {
    @EnvironmentObject var dataManager: NetworkDataManager
    @State private var monthlyChange: Double = 12.5
    @State private var isRecording = false
    @State private var showingManualInput = false
    @State private var showingVoiceRecording = false
    @State private var showingVoiceResult = false
    @State private var showingMultipleExpenses = false
    @State private var voiceRecognitionResult: VoiceRecognitionResult?
    @StateObject private var voiceNetworkService = VoiceNetworkService()

    private var todayRecords: [ExpenseRecord] {
        let calendar = Calendar.current
        let today = Date()
        return dataManager.expenses.filter { calendar.isDate($0.date, equalTo: today, toGranularity: .day) }
    }

    private var totalAmount: Double {
        dataManager.getTotalSpending(for: .monthly)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    HStack {
                        Text("Home")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        Spacer()
                    }
                    .padding(.horizontal)

                    // Total Amount Card
                    VStack(spacing: 8) {
                        Text("Monthly Total Spending")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("\(dataManager.getCurrencySymbol()) \(String(format: "%.2f", totalAmount))")
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(.primary)

                        HStack {
                            Text("vs last month")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("+\(String(format: "%.1f", monthlyChange))%")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.vertical, 20)
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6))
                    .cornerRadius(16)
                    .padding(.horizontal)
                    
                    // Action Buttons
                    HStack(spacing: 16) {
                        // Voice Recording Button
                        Button(action: {
                            startVoiceRecording()
                        }) {
                            VStack(spacing: 8) {
                                Image(systemName: "mic.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                                Text("Voice Recording")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 80)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.purple, Color.blue]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(16)
                            .scaleEffect(isRecording ? 1.1 : 1.0)
                            .animation(.easeInOut(duration: 0.1), value: isRecording)
                        }
                        
                        // Manual Input Button
                        Button(action: {
                            showingManualInput = true
                        }) {
                            VStack(spacing: 8) {
                                Image(systemName: "pencil")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                                Text("Manual Entry")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 80)
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.pink, Color.orange]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .cornerRadius(16)
                        }
                    }
                    .padding(.horizontal)
                    
                    // Today's Records
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text("Today's Records")
                                .font(.headline)
                                .fontWeight(.semibold)
                            Spacer()
                        }
                        .padding(.horizontal)
                        
                        LazyVStack(spacing: 12) {
                            ForEach(todayRecords) { record in
                                RecordRowView(record: record)
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingManualInput) {
            ManualInputView()
                .environmentObject(dataManager)
        }
        .sheet(isPresented: $showingVoiceRecording) {
            VoiceRecordingSheet(
                onRecordingCompleted: handleVoiceRecordingCompleted,
                onRecordingCancelled: handleVoiceRecordingCancelled
            )
        }
        .sheet(isPresented: $showingVoiceResult) {
            NavigationView {
                VStack(spacing: 20) {
                    if let result = voiceRecognitionResult {
                        Text("Voice Recognition Result")
                            .font(.title2)
                            .fontWeight(.bold)

                        VStack(alignment: .leading, spacing: 10) {
                            Text("Amount: \(result.currency) \(String(format: "%.2f", result.amount))")
                            Text("Category: \(result.category)")
                            Text("Merchant: \(result.merchant)")
                            Text("Description: \(result.description)")
                            Text("Confidence: \(Int(result.confidence * 100))%")
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(12)

                        Text("Recognized Text:")
                            .font(.headline)

                        Text(result.recognizedText)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)

                        Spacer()

                        HStack(spacing: 20) {
                            Button("Cancel") {
                                handleVoiceResultCancelled()
                            }
                            .padding()
                            .background(Color.gray)
                            .foregroundColor(.white)
                            .cornerRadius(8)

                            Button("Confirm") {
                                handleVoiceResultConfirmed(result)
                            }
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                        }
                    } else {
                        Text("Processing Voice Recognition...")
                            .font(.title2)
                            .fontWeight(.bold)

                        ProgressView()
                            .scaleEffect(1.5)
                            .padding()

                        if let error = voiceNetworkService.errorMessage {
                            Text("Error: \(error)")
                                .foregroundColor(.red)
                                .padding()
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(8)
                        }

                        Spacer()

                        Button("Close") {
                            handleVoiceResultCancelled()
                        }
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
                .padding()
                .navigationTitle("Voice Recognition")
                .navigationBarTitleDisplayMode(.inline)
            }
        }
        .sheet(isPresented: $showingMultipleExpenses) {
            if let result = voiceRecognitionResult, result.isMultiple {
                MultipleExpensesConfirmationView(
                    result: result,
                    onConfirm: handleMultipleExpensesConfirmed,
                    onCancel: handleMultipleExpensesCancelled
                )
            }
        }
    }
    
    private func startVoiceRecording() {
        showingVoiceRecording = true
    }

    private func handleVoiceRecordingCompleted(_ audioURL: URL) {
        showingVoiceRecording = false

        Task {
            // Show loading state
            await MainActor.run {
                print("🔄 Starting voice upload for: \(audioURL)")
                showingVoiceResult = true  // Show the processing dialog immediately
            }

            if let result = await voiceNetworkService.uploadVoiceRecording(audioURL) {
                await MainActor.run {
                    print("✅ Voice recognition result received: \(result.recognizedText)")
                    voiceRecognitionResult = result

                    // Check if we have multiple expenses
                    if result.isMultiple && result.multipleExpenses != nil {
                        showingVoiceResult = false
                        showingMultipleExpenses = true
                    } else {
                        // Single expense - show normal confirmation
                        // showingVoiceResult is already true, so the UI will update automatically
                    }
                }
            } else {
                await MainActor.run {
                    // Handle upload error - could show an alert
                    if let error = voiceNetworkService.errorMessage {
                        print("❌ Voice upload error: \(error)")
                    } else {
                        print("❌ Voice upload failed with no error message")
                    }

                    // Hide the processing dialog and show error alert
                    showingVoiceResult = false

                    let alertController = UIAlertController(
                        title: "Voice Recognition Failed",
                        message: "Unable to process your voice recording. Please try again.",
                        preferredStyle: .alert
                    )
                    alertController.addAction(UIAlertAction(title: "OK", style: .default))

                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.present(alertController, animated: true)
                    }
                }
            }

            // Clean up the temporary audio file
            try? FileManager.default.removeItem(at: audioURL)
        }
    }

    private func handleVoiceRecordingCancelled() {
        showingVoiceRecording = false
    }

    private func handleVoiceResultConfirmed(_ result: VoiceRecognitionResult) {
        showingVoiceResult = false

        // Create ExpenseRecord from voice recognition result
        let expense = ExpenseRecord(
            id: UUID().uuidString,
            category: result.category,
            amount: result.amount,
            description: result.description,
            time: result.time,
            icon: getCategoryIcon(result.category),
            color: getCategoryColor(result.category),
            date: Date(), // Use current date
            currency: result.currency
        )

        // Add to data manager
        dataManager.addExpense(expense)

        voiceRecognitionResult = nil
    }

    private func handleVoiceResultCancelled() {
        showingVoiceResult = false
        voiceRecognitionResult = nil
    }

    private func handleMultipleExpensesConfirmed(_ expenses: [VoiceRecognitionResult]) {
        showingMultipleExpenses = false

        print("🔍 Debug: handleMultipleExpensesConfirmed called with \(expenses.count) expenses")

        // Create a task to handle all expenses sequentially
        Task {
            // Add each selected expense to the data manager
            for (index, result) in expenses.enumerated() {
                print("🔍 Debug: Processing expense \(index + 1): \(result.description) - \(result.amount)")

                // Create ExpenseRecord from voice recognition result
                let expense = ExpenseRecord(
                    id: UUID().uuidString,
                    category: result.category,
                    amount: result.amount,
                    description: result.description,
                    time: result.time,
                    icon: getCategoryIcon(result.category),
                    color: getCategoryColor(result.category),
                    date: Date(), // Use current date
                    currency: result.currency
                )

                print("🔍 Debug: Created ExpenseRecord: \(expense.description) - \(expense.amount)")

                // Add to data manager and wait for it to complete
                await withCheckedContinuation { continuation in
                    print("🔍 Debug: Starting to add expense \(index + 1)")

                    // Create a task to add the expense
                    Task {
                        // Call the async method and wait for it to complete
                        await dataManager.createExpenseDirectly(expense)

                        print("🔍 Debug: Completed adding expense \(index + 1)")
                        continuation.resume()
                    }
                }

                print("🔍 Debug: Finished processing expense \(index + 1)")
            }

            print("🔍 Debug: Finished processing all expenses")

            // Update UI on main thread
            await MainActor.run {
                voiceRecognitionResult = nil
            }
        }
    }

    private func handleMultipleExpensesCancelled() {
        showingMultipleExpenses = false
        voiceRecognitionResult = nil
    }

    // MARK: - Helper Methods

    private func getCategoryIcon(_ category: String) -> String {
        switch category {
        case "Food & Dining":
            return "fork.knife"
        case "Transportation":
            return "car.fill"
        case "Shopping":
            return "bag.fill"
        case "Entertainment":
            return "tv.fill"
        case "Health & Fitness":
            return "heart.fill"
        default:
            return "questionmark.circle.fill"
        }
    }

    private func getCategoryColor(_ category: String) -> Color {
        switch category {
        case "Food & Dining":
            return .orange
        case "Transportation":
            return .blue
        case "Shopping":
            return .green
        case "Entertainment":
            return .purple
        case "Health & Fitness":
            return .red
        default:
            return .gray
        }
    }
}

struct RecordRowView: View {
    @EnvironmentObject var dataManager: NetworkDataManager
    let record: ExpenseRecord

    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(record.icon)
                .font(.system(size: 20))
                .frame(width: 40, height: 40)
                .background(record.color.opacity(0.2))
                .cornerRadius(8)
            
            // Record Details
            VStack(alignment: .leading, spacing: 2) {
                Text(record.category)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(record.time)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Amount
            Text("\(dataManager.getCurrencySymbol())\(String(format: "%.2f", record.amount))")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    HomeView()
        .environmentObject(NetworkDataManager())
}

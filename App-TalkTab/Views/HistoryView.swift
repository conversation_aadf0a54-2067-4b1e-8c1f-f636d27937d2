//
//  HistoryView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct HistoryView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    @State private var searchText = ""
    
    private var groupedExpenses: [HistorySection] {
        let calendar = Calendar.current
        let formatter = DateFormatter()
        formatter.dateStyle = .medium

        let grouped = Dictionary(grouping: dataManager.expenses) { expense in
            calendar.startOfDay(for: expense.date)
        }

        return grouped.map { date, expenses in
            let dateString: String
            if calendar.isDateInToday(date) {
                dateString = "Today"
            } else if calendar.isDateInYesterday(date) {
                dateString = "Yesterday"
            } else {
                dateString = formatter.string(from: date)
            }

            let totalAmount = expenses.reduce(0) { $0 + $1.amount }
            return HistorySection(date: dateString, totalAmount: totalAmount, records: expenses.sorted { $0.date > $1.date })
        }.sorted { $0.records.first?.date ?? Date() > $1.records.first?.date ?? Date() }
    }

    var filteredRecords: [HistorySection] {
        if searchText.isEmpty {
            return groupedExpenses
        } else {
            return groupedExpenses.compactMap { section in
                let filteredRecords = section.records.filter { record in
                    record.category.localizedCaseInsensitiveContains(searchText) ||
                    record.description.localizedCaseInsensitiveContains(searchText)
                }

                if filteredRecords.isEmpty {
                    return nil
                } else {
                    return HistorySection(
                        date: section.date,
                        totalAmount: filteredRecords.reduce(0) { $0 + $1.amount },
                        records: filteredRecords
                    )
                }
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                HStack {
                    Text("History")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    Spacer()
                    Button(action: {
                        // TODO: Add filter/sort options
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 16)

                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)

                    TextField("Search records", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                    
                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(10)
                .padding(.horizontal)
                .padding(.bottom, 16)
                
                // History List
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(filteredRecords) { section in
                            HistorySectionView(section: section)
                        }
                    }
                    .padding(.horizontal)
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
        }
    }
}

struct HistorySectionView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    let section: HistorySection

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Section Header
            HStack {
                Text(section.date)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(dataManager.getCurrencySymbol())\(String(format: "%.2f", section.totalAmount))")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
            
            // Records
            VStack(spacing: 8) {
                ForEach(section.records) { record in
                    HistoryRecordRowView(record: record)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

struct HistoryRecordRowView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    let record: ExpenseRecord

    var body: some View {
        HStack(spacing: 12) {
            // Category Icon
            Text(record.icon)
                .font(.system(size: 18))
                .frame(width: 36, height: 36)
                .background(record.color.opacity(0.2))
                .cornerRadius(8)
            
            // Record Details
            VStack(alignment: .leading, spacing: 2) {
                Text(record.category)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(record.time)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Amount
            Text("\(dataManager.getCurrencySymbol())\(String(format: "%.2f", record.amount))")
                .font(.system(size: 15, weight: .semibold))
                .foregroundColor(.primary)
        }
        .padding(.vertical, 4)
    }
}

struct HistorySection: Identifiable {
    let id = UUID()
    let date: String
    let totalAmount: Double
    let records: [ExpenseRecord]
}

#Preview {
    HistoryView()
}

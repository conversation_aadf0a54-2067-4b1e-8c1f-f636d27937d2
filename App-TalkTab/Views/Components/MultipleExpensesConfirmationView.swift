//
//  MultipleExpensesConfirmationView.swift
//  App-TalkTab
//
//  View for confirming multiple expenses from voice recognition
//

import SwiftUI

struct MultipleExpensesConfirmationView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedExpenses: Set<String> = []
    
    let result: VoiceRecognitionResult
    let onConfirm: ([VoiceRecognitionResult]) -> Void
    let onCancel: () -> Void
    
    private var expenses: [VoiceRecognitionResult] {
        result.multipleExpenses ?? []
    }
    
    init(result: VoiceRecognitionResult, onConfirm: @escaping ([VoiceRecognitionResult]) -> Void, onCancel: @escaping () -> Void) {
        self.result = result
        self.onConfirm = onConfirm
        self.onCancel = onCancel
        // Initially select all expenses
        self._selectedExpenses = State(initialValue: Set(result.multipleExpenses?.map { $0.id } ?? []))
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    headerCard
                    
                    // Recognized Text
                    recognizedTextCard
                    
                    // Expenses List
                    expensesListCard
                    
                    Spacer(minLength: 20)
                }
                .padding()
            }
            .navigationTitle("Multiple Expenses")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Confirm") {
                        let selectedResults = expenses.filter { selectedExpenses.contains($0.id) }
                        onConfirm(selectedResults)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                    .disabled(selectedExpenses.isEmpty)
                }
            }
        }
    }
    
    private var headerCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Multiple Expenses Detected")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            HStack {
                Text("Found \(expenses.count) expenses in your voice recording")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var recognizedTextCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "waveform")
                    .foregroundColor(.blue)
                Text("Recognized Text")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            Text(result.recognizedText)
                .font(.body)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var expensesListCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "list.bullet")
                    .foregroundColor(.orange)
                Text("Detected Expenses")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                
                Button("Select All") {
                    if selectedExpenses.count == expenses.count {
                        selectedExpenses.removeAll()
                    } else {
                        selectedExpenses = Set(expenses.map { $0.id })
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ForEach(Array(expenses.enumerated()), id: \.element.id) { index, expense in
                expenseRow(expense: expense, index: index + 1)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private func expenseRow(expense: VoiceRecognitionResult, index: Int) -> some View {
        HStack(spacing: 12) {
            // Selection checkbox
            Button(action: {
                if selectedExpenses.contains(expense.id) {
                    selectedExpenses.remove(expense.id)
                } else {
                    selectedExpenses.insert(expense.id)
                }
            }) {
                Image(systemName: selectedExpenses.contains(expense.id) ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedExpenses.contains(expense.id) ? .blue : .gray)
                    .font(.title3)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("Expense \(index)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(expense.currency) \(String(format: "%.2f", expense.amount))")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                
                if !expense.category.isEmpty {
                    Text(expense.category)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                }
                
                if !expense.merchant.isEmpty {
                    Text("at \(expense.merchant)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                if !expense.description.isEmpty {
                    Text(expense.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(selectedExpenses.contains(expense.id) ? Color.blue.opacity(0.05) : Color(.systemGray6))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(selectedExpenses.contains(expense.id) ? Color.blue : Color.clear, lineWidth: 1)
        )
    }
}

struct MultipleExpensesConfirmationView_Previews: PreviewProvider {
    static var previews: some View {
        let mockExpenses = [
            VoiceRecognitionResult(
                id: "1",
                amount: 15.50,
                currency: "SGD",
                category: "Food & Dining",
                merchant: "McDonald's",
                description: "Lunch",
                date: "2025-07-15",
                time: "12:30:00",
                confidence: 0.95,
                recognizedText: "I spent 15 dollars at McDonald's for lunch, then paid 20 dollars for gas",
                rawText: "I spent 15 dollars at McDonald's for lunch"
            ),
            VoiceRecognitionResult(
                id: "2",
                amount: 20.00,
                currency: "SGD",
                category: "Transportation",
                merchant: "Shell",
                description: "Gas",
                date: "2025-07-15",
                time: "12:35:00",
                confidence: 0.90,
                recognizedText: "I spent 15 dollars at McDonald's for lunch, then paid 20 dollars for gas",
                rawText: "then paid 20 dollars for gas"
            )
        ]
        
        let mockResult = VoiceRecognitionResult(
            id: "test",
            amount: 35.50,
            currency: "SGD",
            category: "Multiple",
            merchant: "",
            description: "Multiple expenses detected",
            date: "2025-07-15",
            time: "12:30:00",
            confidence: 0.92,
            recognizedText: "I spent 15 dollars at McDonald's for lunch, then paid 20 dollars for gas",
            rawText: "I spent 15 dollars at McDonald's for lunch, then paid 20 dollars for gas",
            isMultiple: true,
            multipleExpenses: mockExpenses
        )
        
        MultipleExpensesConfirmationView(
            result: mockResult,
            onConfirm: { _ in },
            onCancel: { }
        )
    }
}

//
//  CategoryPickerView.swift
//  App-TalkTab
//
//  Category picker for expense categorization
//

import SwiftUI

struct CategoryPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedCategory: String
    
    let categories: [String]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(categories, id: \.self) { category in
                    CategoryRow(
                        category: category,
                        isSelected: category == selectedCategory
                    ) {
                        selectedCategory = category
                        dismiss()
                    }
                }
            }
            .navigationTitle("Select Category")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct CategoryRow: View {
    let category: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                // Category icon
                Image(systemName: categoryIcon)
                    .foregroundColor(categoryColor)
                    .frame(width: 24, height: 24)
                
                // Category name
                Text(category)
                    .foregroundColor(.primary)
                    .font(.body)
                
                Spacer()
                
                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var categoryIcon: String {
        switch category {
        case "Food & Dining":
            return "fork.knife"
        case "Transportation":
            return "car.fill"
        case "Shopping":
            return "bag.fill"
        case "Entertainment":
            return "tv.fill"
        case "Health & Fitness":
            return "heart.fill"
        default:
            return "questionmark.circle.fill"
        }
    }
    
    private var categoryColor: Color {
        switch category {
        case "Food & Dining":
            return .orange
        case "Transportation":
            return .blue
        case "Shopping":
            return .green
        case "Entertainment":
            return .purple
        case "Health & Fitness":
            return .red
        default:
            return .gray
        }
    }
}

struct CategoryPickerView_Previews: PreviewProvider {
    static var previews: some View {
        CategoryPickerView(
            selectedCategory: .constant("Food & Dining"),
            categories: ["Food & Dining", "Transportation", "Shopping", "Entertainment", "Health & Fitness", "Other"]
        )
    }
}

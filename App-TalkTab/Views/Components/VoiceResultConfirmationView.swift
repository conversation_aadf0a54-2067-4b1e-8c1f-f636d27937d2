//
//  VoiceResultConfirmationView.swift
//  App-TalkTab
//
//  View for confirming and editing voice recognition results
//

import SwiftUI

struct VoiceResultConfirmationView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var editedAmount: String
    @State private var editedCurrency: String
    @State private var editedCategory: String
    @State private var editedMerchant: String
    @State private var editedDescription: String
    @State private var showingCategoryPicker = false

    let result: VoiceRecognitionResult
    let onConfirm: (VoiceRecognitionResult) -> Void
    let onCancel: () -> Void

    private let categories = ["Food & Dining", "Transportation", "Shopping", "Entertainment", "Health & Fitness", "Other"]
    private let currencies = ["SGD", "USD", "MYR", "CNY", "THB"]

    init(result: VoiceRecognitionResult, onConfirm: @escaping (VoiceRecognitionResult) -> Void, onCancel: @escaping () -> Void) {
        self.result = result
        self.onConfirm = onConfirm
        self.onCancel = onCancel
        self._editedAmount = State(initialValue: String(format: "%.2f", result.amount))
        self._editedCurrency = State(initialValue: result.currency)
        self._editedCategory = State(initialValue: result.category)
        self._editedMerchant = State(initialValue: result.merchant)
        self._editedDescription = State(initialValue: result.description)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Recognition Status
                    recognitionStatusCard
                    
                    // Expense Details
                    expenseDetailsCard
                    
                    // Recognized Text
                    recognizedTextCard
                    
                    Spacer(minLength: 20)
                }
                .padding()
            }
            .navigationTitle("Confirm Expense")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Confirm") {
                        let updatedResult = VoiceRecognitionResult(
                            id: result.id,
                            amount: Double(editedAmount) ?? result.amount,
                            currency: editedCurrency,
                            category: editedCategory,
                            merchant: editedMerchant,
                            description: editedDescription,
                            date: result.date,
                            time: result.time,
                            confidence: result.confidence,
                            recognizedText: result.recognizedText,
                            rawText: result.rawText
                        )
                        onConfirm(updatedResult)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCategoryPicker) {
            CategoryPickerView(
                selectedCategory: $editedCategory,
                categories: categories
            )
        }
    }
    
    // MARK: - Recognition Status Card
    
    private var recognitionStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Voice Recognition Complete")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            HStack {
                Text("Confidence:")
                    .foregroundColor(.secondary)

                Text("\(Int(result.confidence * 100))%")
                    .fontWeight(.medium)
                    .foregroundColor(confidenceColor)

                Spacer()
            }
            .font(.caption)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var confidenceColor: Color {
        if result.confidence >= 0.8 {
            return .green
        } else if result.confidence >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Expense Details Card
    
    private var expenseDetailsCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Expense Details")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            VStack(spacing: 16) {
                // Amount
                HStack {
                    Text("Amount:")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)

                    HStack(spacing: 8) {
                        Picker("Currency", selection: $editedCurrency) {
                            ForEach(currencies, id: \.self) { currency in
                                Text(currency).tag(currency)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .frame(width: 60)

                        TextField("Amount", text: $editedAmount)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.decimalPad)
                            .frame(width: 100)
                    }

                    Spacer()
                }
                
                // Category
                HStack {
                    Text("Category:")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)

                    Button(action: {
                        showingCategoryPicker = true
                    }) {
                        HStack {
                            Text(editedCategory)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }

                    Spacer()
                }
                
                // Merchant
                HStack {
                    Text("Merchant:")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)

                    TextField("Enter merchant name", text: $editedMerchant)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Spacer()
                }
                
                // Description
                VStack(alignment: .leading, spacing: 8) {
                    Text("Description:")
                        .foregroundColor(.secondary)
                        .font(.subheadline)

                    TextField("Enter description", text: $editedDescription, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                }
            }
            .font(.subheadline)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - Recognized Text Card
    
    private var recognizedTextCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("What you said:")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            Text(result.recognizedText)
                .font(.body)
                .foregroundColor(.secondary)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
}

struct VoiceResultConfirmationView_Previews: PreviewProvider {
    static var previews: some View {
        VoiceResultConfirmationView(
            result: VoiceRecognitionResult(
                id: "test",
                amount: 15.50,
                currency: "SGD",
                category: "Food & Dining",
                merchant: "McDonald's",
                description: "Lunch at McDonald's",
                date: "2025-07-15",
                time: "12:30:00",
                confidence: 0.95,
                recognizedText: "I spent 15 dollars and 50 cents at McDonald's for lunch",
                rawText: "I spent 15 dollars and 50 cents at McDonald's for lunch"
            ),
            onConfirm: { _ in },
            onCancel: { }
        )
    }
}

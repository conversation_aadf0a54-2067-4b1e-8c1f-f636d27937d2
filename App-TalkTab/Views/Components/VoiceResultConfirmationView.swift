//
//  VoiceResultConfirmationView.swift
//  App-TalkTab
//
//  View for confirming and editing voice recognition results
//

import SwiftUI

struct VoiceResultConfirmationView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var editedResult: VoiceRecognitionResult
    
    let result: VoiceRecognitionResult
    let onConfirm: (VoiceRecognitionResult) -> Void
    let onCancel: () -> Void
    
    init(result: VoiceRecognitionResult, onConfirm: @escaping (VoiceRecognitionResult) -> Void, onCancel: @escaping () -> Void) {
        self.result = result
        self.onConfirm = onConfirm
        self.onCancel = onCancel
        self._editedResult = State(initialValue: result)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Recognition Status
                    recognitionStatusCard
                    
                    // Expense Details
                    expenseDetailsCard
                    
                    // Recognized Text
                    recognizedTextCard
                    
                    Spacer(minLength: 20)
                }
                .padding()
            }
            .navigationTitle("Confirm Expense")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onCancel()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Confirm") {
                        onConfirm(editedResult)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Recognition Status Card
    
    private var recognitionStatusCard: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Voice Recognition Complete")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            HStack {
                Text("Confidence:")
                    .foregroundColor(.secondary)
                
                Text("\(Int(editedResult.confidence * 100))%")
                    .fontWeight(.medium)
                    .foregroundColor(confidenceColor)
                
                Spacer()
            }
            .font(.caption)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var confidenceColor: Color {
        if editedResult.confidence >= 0.8 {
            return .green
        } else if editedResult.confidence >= 0.6 {
            return .orange
        } else {
            return .red
        }
    }
    
    // MARK: - Expense Details Card
    
    private var expenseDetailsCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Expense Details")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            VStack(spacing: 12) {
                // Amount
                HStack {
                    Text("Amount:")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)
                    
                    Text("\(editedResult.currency) \(editedResult.amount, specifier: "%.2f")")
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                
                // Category
                HStack {
                    Text("Category:")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)
                    
                    Text(editedResult.category)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                
                // Merchant
                if !editedResult.merchant.isEmpty {
                    HStack {
                        Text("Merchant:")
                            .foregroundColor(.secondary)
                            .frame(width: 80, alignment: .leading)
                        
                        Text(editedResult.merchant)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                }
                
                // Description
                HStack {
                    Text("Description:")
                        .foregroundColor(.secondary)
                        .frame(width: 80, alignment: .leading)
                    
                    Text(editedResult.description)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
            }
            .font(.subheadline)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    // MARK: - Recognized Text Card
    
    private var recognizedTextCard: some View {
        VStack(spacing: 12) {
            HStack {
                Text("What you said:")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            
            Text(editedResult.recognizedText)
                .font(.body)
                .foregroundColor(.secondary)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
}

struct VoiceResultConfirmationView_Previews: PreviewProvider {
    static var previews: some View {
        VoiceResultConfirmationView(
            result: VoiceRecognitionResult(
                id: "test",
                amount: 15.50,
                currency: "SGD",
                category: "Food & Dining",
                merchant: "McDonald's",
                description: "Lunch at McDonald's",
                date: "2025-07-15",
                time: "12:30:00",
                confidence: 0.95,
                recognizedText: "I spent 15 dollars and 50 cents at McDonald's for lunch",
                rawText: "I spent 15 dollars and 50 cents at McDonald's for lunch"
            ),
            onConfirm: { _ in },
            onCancel: { }
        )
    }
}

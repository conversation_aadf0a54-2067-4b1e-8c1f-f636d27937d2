//
//  VoiceRecordingView.swift
//  App-TalkTab
//
//  Voice recording UI component with visual feedback
//

import SwiftUI

struct VoiceRecordingView: View {
    @StateObject private var voiceService = VoiceRecordingService()
    @State private var showingPermissionAlert = false
    @State private var recordingURL: URL?
    
    // Callback for when recording is completed
    let onRecordingCompleted: (URL) -> Void
    let onRecordingCancelled: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Audio level visualization
            if voiceService.isRecording {
                audioVisualization
            }
            
            // Recording duration
            if voiceService.isRecording {
                Text(voiceService.formatDuration(voiceService.recordingDuration))
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            // Recording button
            recordingButton
            
            // Status text
            statusText
            
            // Action buttons when recording
            if voiceService.isRecording {
                actionButtons
            }
        }
        .padding()
        .onAppear {
            if !voiceService.isPermissionGranted {
                voiceService.requestPermission()
            }
        }
        .alert("Microphone Permission Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                openAppSettings()
            }
            But<PERSON>("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable microphone access in Settings to use voice recording.")
        }
        .alert("Recording Error", isPresented: .constant(voiceService.errorMessage != nil)) {
            Button("OK") {
                voiceService.clearError()
            }
        } message: {
            if let error = voiceService.errorMessage {
                Text(error)
            }
        }
    }
    
    // MARK: - Audio Visualization
    
    private var audioVisualization: some View {
        HStack(spacing: 4) {
            ForEach(0..<20, id: \.self) { index in
                RoundedRectangle(cornerRadius: 2)
                    .fill(Color.blue)
                    .frame(width: 3, height: barHeight(for: index))
                    .animation(.easeInOut(duration: 0.1), value: voiceService.audioLevels)
            }
        }
        .frame(height: 40)
    }
    
    private func barHeight(for index: Int) -> CGFloat {
        let baseHeight: CGFloat = 4
        let maxHeight: CGFloat = 40
        
        // Create a wave-like pattern based on audio levels
        let normalizedIndex = Float(index) / 20.0
        let waveOffset = sin(normalizedIndex * .pi * 2) * 0.3
        let adjustedLevel = max(0, voiceService.audioLevels + waveOffset)
        
        return baseHeight + (maxHeight - baseHeight) * CGFloat(adjustedLevel)
    }
    
    // MARK: - Recording Button
    
    private var recordingButton: some View {
        Button(action: toggleRecording) {
            ZStack {
                Circle()
                    .fill(voiceService.isRecording ? Color.red : Color.blue)
                    .frame(width: 80, height: 80)
                    .scaleEffect(voiceService.isRecording ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: voiceService.isRecording)
                
                Image(systemName: voiceService.isRecording ? "stop.fill" : "mic.fill")
                    .font(.title)
                    .foregroundColor(.white)
            }
        }
        .disabled(!voiceService.isPermissionGranted)
        .opacity(voiceService.isPermissionGranted ? 1.0 : 0.5)
    }
    
    // MARK: - Status Text
    
    private var statusText: some View {
        Group {
            if !voiceService.isPermissionGranted {
                Text("Microphone permission required")
                    .foregroundColor(.red)
                    .font(.caption)
            } else if voiceService.isRecording {
                Text("Recording... Tap to stop")
                    .foregroundColor(.blue)
                    .font(.caption)
            } else {
                Text("Tap to start recording")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
    }
    
    // MARK: - Action Buttons
    
    private var actionButtons: some View {
        HStack(spacing: 20) {
            // Cancel button
            Button(action: cancelRecording) {
                HStack {
                    Image(systemName: "xmark")
                    Text("Cancel")
                }
                .foregroundColor(.red)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.red.opacity(0.1))
                .cornerRadius(20)
            }
            
            // Done button
            Button(action: completeRecording) {
                HStack {
                    Image(systemName: "checkmark")
                    Text("Done")
                }
                .foregroundColor(.green)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.green.opacity(0.1))
                .cornerRadius(20)
            }
        }
    }
    
    // MARK: - Actions
    
    private func toggleRecording() {
        if voiceService.isRecording {
            completeRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        guard voiceService.isPermissionGranted else {
            showingPermissionAlert = true
            return
        }
        
        let success = voiceService.startRecording()
        if !success {
            // Error handling is done through the error message
        }
    }
    
    private func completeRecording() {
        if let url = voiceService.stopRecording() {
            recordingURL = url
            onRecordingCompleted(url)
        }
    }
    
    private func cancelRecording() {
        voiceService.cancelRecording()
        onRecordingCancelled()
    }
    
    private func openAppSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

// MARK: - Preview

struct VoiceRecordingView_Previews: PreviewProvider {
    static var previews: some View {
        VoiceRecordingView(
            onRecordingCompleted: { url in
                print("Recording completed: \(url)")
            },
            onRecordingCancelled: {
                print("Recording cancelled")
            }
        )
        .padding()
    }
}

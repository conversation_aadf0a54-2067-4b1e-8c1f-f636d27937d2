//
//  VoiceRecordingSheet.swift
//  App-TalkTab
//
//  Full-screen voice recording interface
//

import SwiftUI

struct VoiceRecordingSheet: View {
    @Environment(\.dismiss) private var dismiss
    
    let onRecordingCompleted: (URL) -> Void
    let onRecordingCancelled: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Spacer()
                
                // Title
                VStack(spacing: 8) {
                    Text("Voice Recording")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Describe your expense")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Voice Recording Component
                VoiceRecordingView(
                    onRecordingCompleted: { url in
                        onRecordingCompleted(url)
                        dismiss()
                    },
                    onRecordingCancelled: {
                        onRecordingCancelled()
                        dismiss()
                    }
                )
                
                Spacer()
                
                // Tips
                VStack(spacing: 12) {
                    Text("💡 Tips for better recognition:")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("•")
                            Text("Speak clearly and at normal pace")
                        }
                        HStack {
                            Text("•")
                            Text("Include amount, merchant, and purpose")
                        }
                        HStack {
                            Text("•")
                            Text("Example: \"I spent 15 dollars at McDonald's for lunch\"")
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onRecordingCancelled()
                        dismiss()
                    }
                }
            }
        }
    }
}

struct VoiceRecordingSheet_Previews: PreviewProvider {
    static var previews: some View {
        VoiceRecordingSheet(
            onRecordingCompleted: { _ in },
            onRecordingCancelled: { }
        )
    }
}

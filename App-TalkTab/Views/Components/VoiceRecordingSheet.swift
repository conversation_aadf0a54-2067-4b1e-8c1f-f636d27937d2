//
//  VoiceRecordingSheet.swift
//  App-TalkTab
//
//  Full-screen voice recording interface
//

import SwiftUI

struct VoiceRecordingSheet: View {
    @Environment(\.dismiss) private var dismiss
    
    let onRecordingCompleted: (URL) -> Void
    let onRecordingCancelled: () -> Void
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Spacer()
                
                // Title
                VStack(spacing: 8) {
                    Text("Voice Recording")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Describe your expense")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Voice Recording Component
                VoiceRecordingView(
                    onRecordingCompleted: { url in
                        onRecordingCompleted(url)
                        dismiss()
                    },
                    onRecordingCancelled: {
                        onRecordingCancelled()
                        dismiss()
                    }
                )
                
                Spacer()
                
                // Tips
                VStack(spacing: 16) {
                    Text("💡 Tips for better recognition:")
                        .font(.headline)
                        .foregroundColor(.primary)

                    VStack(alignment: .leading, spacing: 12) {
                        TipRow(icon: "mic.fill", text: "Speak clearly and at normal pace")
                        TipRow(icon: "dollarsign.circle.fill", text: "Include amount, merchant, and purpose")
                        TipRow(icon: "quote.bubble.fill", text: "Example: \"I spent 15 dollars at McDonald's for lunch\"")
                        TipRow(icon: "globe.asia.australia.fill", text: "Supports English and Chinese")
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onRecordingCancelled()
                        dismiss()
                    }
                }
            }
        }
    }
}

struct TipRow: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 16, height: 16)
                .font(.caption)

            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)

            Spacer()
        }
    }
}

struct VoiceRecordingSheet_Previews: PreviewProvider {
    static var previews: some View {
        VoiceRecordingSheet(
            onRecordingCompleted: { _ in },
            onRecordingCancelled: { }
        )
    }
}

//
//  StatisticsView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct StatisticsView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    @State private var selectedPeriod = 0
    @State private var monthlyChange: Double = 12.5

    private var currentPeriod: StatisticsPeriod {
        StatisticsPeriod(rawValue: selectedPeriod) ?? .monthly
    }

    private var totalAmount: Double {
        dataManager.getTotalSpending(for: currentPeriod)
    }

    private var averageDaily: Double {
        dataManager.getDailyAverage(for: currentPeriod)
    }

    private var monthlyData: [ChartData] {
        dataManager.getMonthlyTrends()
    }

    private var categoryData: [CategoryData] {
        dataManager.getCategoryBreakdown()
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    HStack {
                        Text("Statistics")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        Spacer()
                    }
                    .padding(.horizontal)

                    // Period Selector
                    Picker("Period", selection: $selectedPeriod) {
                        Text("Monthly").tag(0)
                        Text("Quarterly").tag(1)
                        Text("Yearly").tag(2)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.horizontal)
                    
                    // Summary Cards
                    HStack(spacing: 16) {
                        StatCard(
                            title: "Total Spending",
                            value: "\(dataManager.getCurrencySymbol())\(String(format: "%.0f", totalAmount))",
                            subtitle: "vs last month +\(String(format: "%.1f", monthlyChange))%",
                            color: .blue
                        )

                        StatCard(
                            title: "Daily Average",
                            value: "\(dataManager.getCurrencySymbol())\(String(format: "%.0f", averageDaily))",
                            subtitle: currentPeriod.displayName.lowercased(),
                            color: .green
                        )
                    }
                    .padding(.horizontal)
                    
                    // Spending Trend Chart
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Spending Trends")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)
                        
                        SimpleBarChartView(data: monthlyData)
                            .frame(height: 200)
                            .padding(.horizontal)
                    }
                    .padding(.vertical)
                    .background(Color(.systemGray6))
                    .cornerRadius(16)
                    .padding(.horizontal)
                    
                    // Category Distribution
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Category Analysis")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .padding(.horizontal)
                        
                        LazyVStack(spacing: 8) {
                            ForEach(categoryData) { category in
                                CategoryRowView(category: category, total: totalAmount)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .padding(.vertical)
                    .background(Color(.systemGray6))
                    .cornerRadius(16)
                    .padding(.horizontal)
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

struct CategoryRowView: View {
    @EnvironmentObject var dataManager: ExpenseDataManager
    let category: CategoryData
    let total: Double

    var body: some View {
        HStack(spacing: 12) {
            // Category Color Indicator
            Circle()
                .fill(category.color)
                .frame(width: 12, height: 12)
            
            // Category Name
            Text(category.category)
                .font(.system(size: 14))
                .foregroundColor(.primary)
            
            Spacer()
            
            // Progress Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 4)
                        .cornerRadius(2)
                    
                    Rectangle()
                        .fill(category.color)
                        .frame(width: geometry.size.width * (category.percentage / 100), height: 4)
                        .cornerRadius(2)
                }
            }
            .frame(width: 60, height: 4)
            
            // Amount and Percentage
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(dataManager.getCurrencySymbol())\(String(format: "%.0f", category.amount))")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                
                Text("\(String(format: "%.1f", category.percentage))%")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
}



struct SimpleBarChartView: View {
    let data: [ChartData]

    var body: some View {
        let maxAmount = data.map { $0.amount }.max() ?? 1

        HStack(alignment: .bottom, spacing: 12) {
            ForEach(data) { item in
                VStack(spacing: 4) {
                    Rectangle()
                        .fill(Color.blue.gradient)
                        .frame(width: 30, height: CGFloat(item.amount / maxAmount) * 150)
                        .cornerRadius(4)

                    Text(item.period)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
    }
}

#Preview {
    StatisticsView()
        .environmentObject(ExpenseDataManager())
}

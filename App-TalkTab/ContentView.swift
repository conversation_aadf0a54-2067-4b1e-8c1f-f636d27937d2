//
//  ContentView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var dataManager = NetworkDataManager()
    @State private var selectedTab = 0
    @State private var isInitialized = false

    var body: some View {
        Group {
            if isInitialized {
                TabView(selection: $selectedTab) {
                    HomeView()
                        .environmentObject(dataManager)
                        .tabItem {
                            Image(systemName: "house.fill")
                            Text("Home")
                        }
                        .tag(0)

                    StatisticsView()
                        .environmentObject(dataManager)
                        .tabItem {
                            Image(systemName: "chart.bar.fill")
                            Text("Statistics")
                        }
                        .tag(1)

                    HistoryView()
                        .environmentObject(dataManager)
                        .tabItem {
                            Image(systemName: "clock.fill")
                            Text("History")
                        }
                        .tag(2)

                    SettingsView()
                        .environmentObject(dataManager)
                        .tabItem {
                            Image(systemName: "gearshape.fill")
                            Text("Settings")
                        }
                        .tag(3)
                }
                .accentColor(.blue)
            } else {
                // Loading screen
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    Text("Loading TalkTab...")
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(.systemBackground))
            }
        }
        .onAppear {
            // Give the data manager a moment to initialize
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                isInitialized = true
            }
        }
    }
}



#Preview {
    ContentView()
}

//
//  ContentView.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var dataManager = NetworkDataManager()
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .environmentObject(dataManager)
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(0)

            StatisticsView()
                .environmentObject(dataManager)
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("Statistics")
                }
                .tag(1)

            HistoryView()
                .environmentObject(dataManager)
                .tabItem {
                    Image(systemName: "clock.fill")
                    Text("History")
                }
                .tag(2)

            SettingsView()
                .environmentObject(dataManager)
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(3)
        }
        .accentColor(.blue)
    }
}

#Preview {
    ContentView()
}

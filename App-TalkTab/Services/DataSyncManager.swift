//
//  DataSyncManager.swift
//  App-TalkTab
//
//  Created by zc<PERSON><PERSON> on 14/7/25.
//

import Foundation
import SwiftUI
import Network

// MARK: - Sync Operation Types
enum SyncOperationType: String, Codable {
    case create = "CREATE"
    case update = "UPDATE"
    case delete = "DELETE"
}

struct PendingSyncOperation: Codable, Identifiable {
    let id: String
    let type: SyncOperationType
    let timestamp: Date
    let expenseData: ExpenseRecord?
    let expenseId: String?
    
    init(id: String = UUID().uuidString, type: SyncOperationType, expenseData: ExpenseRecord? = nil, expenseId: String? = nil) {
        self.id = id
        self.type = type
        self.timestamp = Date()
        self.expenseData = expenseData
        self.expenseId = expenseId
    }
}

// MARK: - Auto Data Sync Manager
@MainActor
class DataSyncManager: ObservableObject {
    @Published private var pendingOperations: [PendingSyncOperation] = []
    @Published private var isSyncing = false
    @Published private var isNetworkAvailable = false

    private let userDefaults = UserDefaults.standard
    private let pendingOperationsKey = "PendingSyncOperations"
    private let networkMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor")

    private weak var networkDataManager: NetworkDataManager?
    private var syncTimer: Timer?

    init() {
        loadPendingOperations()
        startNetworkMonitoring()
        startPeriodicSync()
    }

    deinit {
        networkMonitor.cancel()
        syncTimer?.invalidate()
    }

    func setNetworkDataManager(_ manager: NetworkDataManager) {
        self.networkDataManager = manager
        // Try to sync immediately when network manager is available
        if isNetworkAvailable && !pendingOperations.isEmpty {
            Task {
                await performAutoSync()
            }
        }
    }

    // MARK: - Network Monitoring

    private func startNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                let wasAvailable = self?.isNetworkAvailable ?? false
                self?.isNetworkAvailable = path.status == .satisfied

                // If network just became available, try to sync
                if !wasAvailable && path.status == .satisfied {
                    NSLog("🌐 Network connection restored, attempting auto-sync")
                    Task {
                        await self?.performAutoSync()
                    }
                }
            }
        }
        networkMonitor.start(queue: monitorQueue)
    }

    // MARK: - Periodic Sync

    private func startPeriodicSync() {
        // Check for pending operations every 30 seconds
        syncTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performAutoSync()
            }
        }
    }

    // MARK: - Pending Operations Management
    
    func addPendingOperation(_ operation: PendingSyncOperation) {
        // Remove any existing operation for the same expense to avoid conflicts
        if let expenseId = operation.expenseData?.id ?? operation.expenseId {
            pendingOperations.removeAll { existingOp in
                (existingOp.expenseData?.id == expenseId) || (existingOp.expenseId == expenseId)
            }
        }

        pendingOperations.append(operation)
        savePendingOperations()

        NSLog("📝 Added pending sync operation: \(operation.type.rawValue)")

        // Try to sync immediately if network is available
        if isNetworkAvailable {
            Task {
                await performAutoSync()
            }
        }
    }
    
    private func removePendingOperation(_ operationId: String) {
        pendingOperations.removeAll { $0.id == operationId }
        savePendingOperations()
    }
    
    private func savePendingOperations() {
        do {
            let encoded = try JSONEncoder().encode(pendingOperations)
            userDefaults.set(encoded, forKey: pendingOperationsKey)
            userDefaults.synchronize()
        } catch {
            NSLog("❌ Failed to save pending operations: \(error)")
        }
    }
    
    private func loadPendingOperations() {
        guard let data = userDefaults.data(forKey: pendingOperationsKey) else { return }
        
        do {
            pendingOperations = try JSONDecoder().decode([PendingSyncOperation].self, from: data)
            NSLog("📂 Loaded \(pendingOperations.count) pending sync operations")
        } catch {
            NSLog("❌ Failed to load pending operations: \(error)")
            pendingOperations = []
        }
    }
    
    // MARK: - Auto Sync Operations

    private func performAutoSync() async {
        guard !isSyncing else { return }
        guard !pendingOperations.isEmpty else { return }
        guard isNetworkAvailable else { return }
        guard let networkManager = networkDataManager else { return }
        guard networkManager.currentUser != nil else { return }

        isSyncing = true
        NSLog("🔄 Starting auto-sync of \(pendingOperations.count) operations")
        
        let operationsToSync = pendingOperations.sorted { $0.timestamp < $1.timestamp }
        var successfulOperations: [String] = []

        for operation in operationsToSync {
            do {
                let success = try await syncSingleOperation(operation, networkManager: networkManager)
                if success {
                    successfulOperations.append(operation.id)
                }
            } catch {
                NSLog("❌ Auto-sync error for \(operation.type.rawValue): \(error)")
                // Continue with other operations
            }
        }

        // Remove successful operations
        for operationId in successfulOperations {
            removePendingOperation(operationId)
        }

        if !successfulOperations.isEmpty {
            NSLog("✅ Auto-synced \(successfulOperations.count) operations successfully")
        }

        isSyncing = false
    }
    
    private func syncSingleOperation(_ operation: PendingSyncOperation, networkManager: NetworkDataManager) async throws -> Bool {
        switch operation.type {
        case .create:
            guard let expenseData = operation.expenseData else { return false }
            return try await syncCreateOperation(expenseData, networkManager: networkManager)
            
        case .update:
            guard let expenseData = operation.expenseData else { return false }
            return try await syncUpdateOperation(expenseData, networkManager: networkManager)
            
        case .delete:
            guard let expenseId = operation.expenseId else { return false }
            return try await syncDeleteOperation(expenseId, networkManager: networkManager)
        }
    }
    
    private func syncCreateOperation(_ expense: ExpenseRecord, networkManager: NetworkDataManager) async throws -> Bool {
        // Check if user is available
        guard let userId = networkManager.currentUser?.id else {
            NSLog("❌ No user available for create sync")
            return false
        }
        
        // Find category
        guard let category = networkManager.categories.first(where: { $0.name == expense.category }) else {
            NSLog("❌ Category not found for create sync: \(expense.category)")
            return false
        }
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            let backendExpense = try await networkManager.networkService.createExpense(
                userId: userId,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            // Update local expense with backend ID
            let updatedExpense = ExpenseRecord(from: backendExpense, currency: networkManager.selectedCurrency)
            if let index = networkManager.expenses.firstIndex(where: { $0.id == expense.id }) {
                networkManager.expenses[index] = updatedExpense
            }
            
            return true
        } catch {
            NSLog("❌ Failed to sync create operation: \(error)")
            return false
        }
    }
    
    private func syncUpdateOperation(_ expense: ExpenseRecord, networkManager: NetworkDataManager) async throws -> Bool {
        guard let userId = networkManager.currentUser?.id else { return false }
        
        guard let category = networkManager.categories.first(where: { $0.name == expense.category }) else {
            return false
        }
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            _ = try await networkManager.networkService.updateExpense(
                userId: userId,
                expenseId: expense.id,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            return true
        } catch {
            NSLog("❌ Failed to sync update operation: \(error)")
            return false
        }
    }
    
    private func syncDeleteOperation(_ expenseId: String, networkManager: NetworkDataManager) async throws -> Bool {
        guard let userId = networkManager.currentUser?.id else { return false }
        
        do {
            try await networkManager.networkService.deleteExpense(userId: userId, expenseId: expenseId)
            return true
        } catch {
            NSLog("❌ Failed to sync delete operation: \(error)")
            return false
        }
    }
    
    // MARK: - Public Interface (for debugging/monitoring)

    func getPendingOperationsCount() -> Int {
        return pendingOperations.count
    }

    func getNetworkStatus() -> Bool {
        return isNetworkAvailable
    }
}

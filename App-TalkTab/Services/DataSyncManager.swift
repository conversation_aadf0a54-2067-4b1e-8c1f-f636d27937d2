//
//  DataSyncManager.swift
//  App-TalkTab
//
//  Created by z<PERSON><PERSON><PERSON> on 14/7/25.
//

import Foundation
import SwiftUI

// MARK: - Sync Operation Types
enum SyncOperationType: String, Codable {
    case create = "CREATE"
    case update = "UPDATE"
    case delete = "DELETE"
}

struct PendingSyncOperation: Codable, Identifiable {
    let id: String
    let type: SyncOperationType
    let timestamp: Date
    let expenseData: ExpenseRecord?
    let expenseId: String?
    
    init(id: String = UUID().uuidString, type: SyncOperationType, expenseData: ExpenseRecord? = nil, expenseId: String? = nil) {
        self.id = id
        self.type = type
        self.timestamp = Date()
        self.expenseData = expenseData
        self.expenseId = expenseId
    }
}

// MARK: - Data Sync Manager
@MainActor
class DataSyncManager: ObservableObject {
    @Published var pendingOperations: [PendingSyncOperation] = []
    @Published var isSyncing = false
    @Published var lastSyncDate: Date?
    @Published var syncStatus: String = "Ready"
    
    private let userDefaults = UserDefaults.standard
    private let pendingOperationsKey = "PendingSyncOperations"
    private let lastSyncDateKey = "LastSyncDate"
    
    private weak var networkDataManager: NetworkDataManager?
    
    init() {
        loadPendingOperations()
        loadLastSyncDate()
    }
    
    func setNetworkDataManager(_ manager: NetworkDataManager) {
        self.networkDataManager = manager
    }
    
    // MARK: - Pending Operations Management
    
    func addPendingOperation(_ operation: PendingSyncOperation) {
        // Remove any existing operation for the same expense to avoid conflicts
        if let expenseId = operation.expenseData?.id ?? operation.expenseId {
            pendingOperations.removeAll { existingOp in
                (existingOp.expenseData?.id == expenseId) || (existingOp.expenseId == expenseId)
            }
        }
        
        pendingOperations.append(operation)
        savePendingOperations()
        
        NSLog("📝 Added pending sync operation: \(operation.type.rawValue) for expense: \(operation.expenseData?.description ?? operation.expenseId ?? "unknown")")
    }
    
    func removePendingOperation(_ operationId: String) {
        pendingOperations.removeAll { $0.id == operationId }
        savePendingOperations()
    }
    
    private func savePendingOperations() {
        do {
            let encoded = try JSONEncoder().encode(pendingOperations)
            userDefaults.set(encoded, forKey: pendingOperationsKey)
            userDefaults.synchronize()
        } catch {
            NSLog("❌ Failed to save pending operations: \(error)")
        }
    }
    
    private func loadPendingOperations() {
        guard let data = userDefaults.data(forKey: pendingOperationsKey) else { return }
        
        do {
            pendingOperations = try JSONDecoder().decode([PendingSyncOperation].self, from: data)
            NSLog("📂 Loaded \(pendingOperations.count) pending sync operations")
        } catch {
            NSLog("❌ Failed to load pending operations: \(error)")
            pendingOperations = []
        }
    }
    
    private func loadLastSyncDate() {
        if let date = userDefaults.object(forKey: lastSyncDateKey) as? Date {
            lastSyncDate = date
        }
    }
    
    private func saveLastSyncDate() {
        userDefaults.set(lastSyncDate, forKey: lastSyncDateKey)
        userDefaults.synchronize()
    }
    
    // MARK: - Sync Operations
    
    func syncPendingOperations() async {
        guard !isSyncing else {
            NSLog("⚠️ Sync already in progress")
            return
        }
        
        guard !pendingOperations.isEmpty else {
            NSLog("ℹ️ No pending operations to sync")
            return
        }
        
        guard let networkManager = networkDataManager else {
            NSLog("❌ NetworkDataManager not available for sync")
            return
        }
        
        isSyncing = true
        syncStatus = "Syncing..."
        
        NSLog("🔄 Starting sync of \(pendingOperations.count) pending operations")
        
        let operationsToSync = pendingOperations.sorted { $0.timestamp < $1.timestamp }
        var successfulOperations: [String] = []
        var failedOperations: [PendingSyncOperation] = []
        
        for operation in operationsToSync {
            do {
                let success = try await syncSingleOperation(operation, networkManager: networkManager)
                if success {
                    successfulOperations.append(operation.id)
                    NSLog("✅ Successfully synced operation: \(operation.type.rawValue)")
                } else {
                    failedOperations.append(operation)
                    NSLog("❌ Failed to sync operation: \(operation.type.rawValue)")
                }
            } catch {
                NSLog("❌ Error syncing operation \(operation.type.rawValue): \(error)")
                failedOperations.append(operation)
            }
        }
        
        // Remove successful operations
        for operationId in successfulOperations {
            removePendingOperation(operationId)
        }
        
        // Update sync status
        if failedOperations.isEmpty {
            syncStatus = "Sync completed successfully"
            lastSyncDate = Date()
            saveLastSyncDate()
            NSLog("🎉 All operations synced successfully")
        } else {
            syncStatus = "Sync completed with \(failedOperations.count) failures"
            NSLog("⚠️ Sync completed with \(failedOperations.count) failed operations")
        }
        
        isSyncing = false
        
        // Clear status after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.syncStatus = "Ready"
        }
    }
    
    private func syncSingleOperation(_ operation: PendingSyncOperation, networkManager: NetworkDataManager) async throws -> Bool {
        switch operation.type {
        case .create:
            guard let expenseData = operation.expenseData else { return false }
            return try await syncCreateOperation(expenseData, networkManager: networkManager)
            
        case .update:
            guard let expenseData = operation.expenseData else { return false }
            return try await syncUpdateOperation(expenseData, networkManager: networkManager)
            
        case .delete:
            guard let expenseId = operation.expenseId else { return false }
            return try await syncDeleteOperation(expenseId, networkManager: networkManager)
        }
    }
    
    private func syncCreateOperation(_ expense: ExpenseRecord, networkManager: NetworkDataManager) async throws -> Bool {
        // Check if user is available
        guard let userId = networkManager.currentUser?.id else {
            NSLog("❌ No user available for create sync")
            return false
        }
        
        // Find category
        guard let category = networkManager.categories.first(where: { $0.name == expense.category }) else {
            NSLog("❌ Category not found for create sync: \(expense.category)")
            return false
        }
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            let backendExpense = try await networkManager.networkService.createExpense(
                userId: userId,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            // Update local expense with backend ID
            let updatedExpense = ExpenseRecord(from: backendExpense, currency: networkManager.selectedCurrency)
            if let index = networkManager.expenses.firstIndex(where: { $0.id == expense.id }) {
                networkManager.expenses[index] = updatedExpense
            }
            
            return true
        } catch {
            NSLog("❌ Failed to sync create operation: \(error)")
            return false
        }
    }
    
    private func syncUpdateOperation(_ expense: ExpenseRecord, networkManager: NetworkDataManager) async throws -> Bool {
        guard let userId = networkManager.currentUser?.id else { return false }
        
        guard let category = networkManager.categories.first(where: { $0.name == expense.category }) else {
            return false
        }
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            _ = try await networkManager.networkService.updateExpense(
                userId: userId,
                expenseId: expense.id,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            return true
        } catch {
            NSLog("❌ Failed to sync update operation: \(error)")
            return false
        }
    }
    
    private func syncDeleteOperation(_ expenseId: String, networkManager: NetworkDataManager) async throws -> Bool {
        guard let userId = networkManager.currentUser?.id else { return false }
        
        do {
            try await networkManager.networkService.deleteExpense(userId: userId, expenseId: expenseId)
            return true
        } catch {
            NSLog("❌ Failed to sync delete operation: \(error)")
            return false
        }
    }
    
    // MARK: - Public Interface
    
    func hasPendingOperations() -> Bool {
        return !pendingOperations.isEmpty
    }
    
    func getPendingOperationsCount() -> Int {
        return pendingOperations.count
    }
    
    func clearAllPendingOperations() {
        pendingOperations.removeAll()
        savePendingOperations()
        NSLog("🗑️ Cleared all pending sync operations")
    }
}

//
//  VoiceRecordingService.swift
//  App-TalkTab
//
//  Voice recording service using AVFoundation
//  <PERSON><PERSON> microphone permissions, audio recording, and file management
//

import Foundation
import AVFoundation
import SwiftUI

@MainActor
class VoiceRecordingService: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var isPermissionGranted = false
    @Published var recordingDuration: TimeInterval = 0
    @Published var audioLevels: Float = 0.0
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var audioRecorder: AVAudioRecorder?
    private var audioSession: AVAudioSession = AVAudioSession.sharedInstance()
    private var recordingTimer: Timer?
    private var levelTimer: Timer?
    private var recordingStartTime: Date?
    
    // Recording settings optimized for speech recognition
    private let recordingSettings: [String: Any] = [
        AVFormatIDKey: Int(kAudioFormatLinearPCM),
        AVSampleRateKey: 16000.0,  // 16kHz for Qwen Omni
        AVNumberOfChannelsKey: 1,   // Mono
        AVLinearPCMBitDepthKey: 16,
        AVLinearPCMIsFloatKey: false,
        AVLinearPCMIsBigEndianKey: false,
        AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
    ]
    
    override init() {
        super.init()
        setupAudioSession()
        checkPermissions()
    }
    
    // MARK: - Audio Session Setup
    
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker])
            try audioSession.setActive(true)
            NSLog("🎤 Audio session configured successfully")
        } catch {
            NSLog("❌ Failed to setup audio session: \(error)")
            errorMessage = "Failed to setup audio session: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Permissions
    
    private func checkPermissions() {
        switch audioSession.recordPermission {
        case .granted:
            isPermissionGranted = true
            NSLog("🔓 Microphone permission already granted")
        case .denied:
            isPermissionGranted = false
            NSLog("🔒 Microphone permission denied")
        case .undetermined:
            requestPermission()
        @unknown default:
            isPermissionGranted = false
        }
    }
    
    func requestPermission() {
        audioSession.requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                self?.isPermissionGranted = granted
                if granted {
                    NSLog("✅ Microphone permission granted")
                } else {
                    NSLog("❌ Microphone permission denied")
                    self?.errorMessage = "Microphone permission is required for voice recording"
                }
            }
        }
    }
    
    // MARK: - Recording Control
    
    func startRecording() -> Bool {
        guard isPermissionGranted else {
            errorMessage = "Microphone permission not granted"
            return false
        }
        
        guard !isRecording else {
            NSLog("⚠️ Recording already in progress")
            return false
        }
        
        // Generate unique filename
        let fileName = "voice_\(Int(Date().timeIntervalSince1970)).wav"
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            // Create audio recorder
            audioRecorder = try AVAudioRecorder(url: audioURL, settings: recordingSettings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true
            
            // Start recording
            let success = audioRecorder?.record() ?? false
            
            if success {
                isRecording = true
                recordingStartTime = Date()
                startTimers()
                NSLog("🎤 Recording started: \(fileName)")
                return true
            } else {
                errorMessage = "Failed to start recording"
                NSLog("❌ Failed to start recording")
                return false
            }
            
        } catch {
            errorMessage = "Recording error: \(error.localizedDescription)"
            NSLog("❌ Recording error: \(error)")
            return false
        }
    }
    
    func stopRecording() -> URL? {
        guard isRecording else {
            NSLog("⚠️ No recording in progress")
            return nil
        }
        
        audioRecorder?.stop()
        stopTimers()
        
        let recordingURL = audioRecorder?.url
        isRecording = false
        recordingDuration = 0
        audioLevels = 0.0
        
        NSLog("🛑 Recording stopped")
        return recordingURL
    }
    
    func cancelRecording() {
        guard isRecording else { return }
        
        let recordingURL = audioRecorder?.url
        audioRecorder?.stop()
        stopTimers()
        
        // Delete the recording file
        if let url = recordingURL {
            try? FileManager.default.removeItem(at: url)
            NSLog("🗑️ Recording cancelled and file deleted")
        }
        
        isRecording = false
        recordingDuration = 0
        audioLevels = 0.0
    }
    
    // MARK: - Timer Management
    
    private func startTimers() {
        // Duration timer
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self, let startTime = self.recordingStartTime else { return }
            self.recordingDuration = Date().timeIntervalSince(startTime)
        }
        
        // Audio level timer
        levelTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateAudioLevels()
        }
    }
    
    private func stopTimers() {
        recordingTimer?.invalidate()
        recordingTimer = nil
        levelTimer?.invalidate()
        levelTimer = nil
        recordingStartTime = nil
    }
    
    private func updateAudioLevels() {
        guard let recorder = audioRecorder, recorder.isRecording else { return }
        
        recorder.updateMeters()
        let averagePower = recorder.averagePower(forChannel: 0)
        
        // Convert decibel to linear scale (0.0 to 1.0)
        let normalizedLevel = pow(10.0, averagePower / 20.0)
        audioLevels = Float(normalizedLevel)
    }
    
    // MARK: - Utility Methods
    
    func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - File Management
    
    func getRecordingsDirectory() -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let recordingsDir = documentsPath.appendingPathComponent("Recordings")
        
        // Create directory if it doesn't exist
        if !FileManager.default.fileExists(atPath: recordingsDir.path) {
            try? FileManager.default.createDirectory(at: recordingsDir, withIntermediateDirectories: true)
        }
        
        return recordingsDir
    }
    
    func deleteRecording(at url: URL) {
        do {
            try FileManager.default.removeItem(at: url)
            NSLog("🗑️ Deleted recording: \(url.lastPathComponent)")
        } catch {
            NSLog("❌ Failed to delete recording: \(error)")
        }
    }
}

// MARK: - AVAudioRecorderDelegate

extension VoiceRecordingService: AVAudioRecorderDelegate {
    
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if flag {
            NSLog("✅ Recording finished successfully")
        } else {
            NSLog("❌ Recording finished with error")
            errorMessage = "Recording failed to complete"
        }
        
        isRecording = false
        stopTimers()
    }
    
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        NSLog("❌ Recording encode error: \(error?.localizedDescription ?? "Unknown error")")
        errorMessage = "Recording error: \(error?.localizedDescription ?? "Unknown error")"
        
        isRecording = false
        stopTimers()
    }
}

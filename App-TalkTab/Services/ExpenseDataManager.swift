//
//  ExpenseDataManager.swift
//  App-TalkTab
//
//  Created by zcbob on 11/7/25.
//

import Foundation
import SwiftUI

class ExpenseDataManager: ObservableObject {
    @Published var expenses: [ExpenseRecord] = []
    @Published var categories: [ExpenseCategory] = []
    @Published var selectedCurrency: String = "USD" {
        didSet {
            saveSettingsToUserDefaults()
        }
    }
    
    init() {
        loadDefaultCategories()
        loadPersistedData()
    }
    
    // MARK: - Categories Management
    private func loadDefaultCategories() {
        categories = [
            ExpenseCategory(id: "1", name: "Food & Dining", icon: "🍽️", color: .orange, subcategories: ["Breakfast", "Lunch", "Dinner", "Snacks", "Coffee"]),
            ExpenseCategory(id: "2", name: "Transportation", icon: "🚗", color: .blue, subcategories: ["Subway", "Bus", "Taxi", "Parking", "Gas"]),
            ExpenseCategory(id: "3", name: "Shopping", icon: "🛍️", color: .green, subcategories: ["Clothing", "Daily Items", "Electronics", "Books"]),
            ExpenseCategory(id: "4", name: "Entertainment", icon: "🎬", color: .purple, subcategories: ["Movies", "Games", "Sports", "Music"]),
            ExpenseCategory(id: "5", name: "Healthcare", icon: "🏥", color: .red, subcategories: ["Doctor", "Medicine", "Checkup", "Dental"]),
            ExpenseCategory(id: "6", name: "Bills & Utilities", icon: "📄", color: .yellow, subcategories: ["Electricity", "Water", "Internet", "Phone"]),
            ExpenseCategory(id: "7", name: "Education", icon: "📚", color: .indigo, subcategories: ["Courses", "Books", "Supplies", "Tuition"]),
            ExpenseCategory(id: "8", name: "Others", icon: "📝", color: .gray, subcategories: ["Miscellaneous", "Gifts", "Donations"])
        ]
    }
    
    // MARK: - Sample Data
    private func loadSampleData() {
        let calendar = Calendar.current
        let today = Date()
        let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
        let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: today)!
        
        expenses = [
            // Today's expenses
            ExpenseRecord(id: "1", category: "Lunch", amount: 45.00, description: "Lunch at Italian restaurant", time: "12:30", icon: "🍽️", color: .orange, date: today),
            ExpenseRecord(id: "2", category: "Coffee", amount: 8.00, description: "Morning coffee", time: "10:15", icon: "☕", color: .brown, date: today),
            ExpenseRecord(id: "3", category: "Coffee", amount: 28.00, description: "Coffee with friends", time: "09:45", icon: "☕", color: .brown, date: today),
            
            // Yesterday's expenses
            ExpenseRecord(id: "4", category: "Movie Ticket", amount: 58.00, description: "Cinema tickets", time: "19:30", icon: "🎬", color: .purple, date: yesterday),
            ExpenseRecord(id: "5", category: "Dinner", amount: 68.50, description: "Dinner with family", time: "18:15", icon: "🍽️", color: .orange, date: yesterday),
            ExpenseRecord(id: "6", category: "Taxi", amount: 30.00, description: "Taxi home", time: "21:45", icon: "🚗", color: .blue, date: yesterday),
            
            // Two days ago
            ExpenseRecord(id: "7", category: "Shopping", amount: 89.00, description: "Grocery shopping", time: "15:20", icon: "🛍️", color: .green, date: twoDaysAgo),
            ExpenseRecord(id: "8", category: "Gas", amount: 65.00, description: "Gas station", time: "08:30", icon: "⛽", color: .blue, date: twoDaysAgo),
        ]
    }
    
    // MARK: - Data Persistence
    private func loadPersistedData() {
        loadExpensesFromUserDefaults()
        loadSettingsFromUserDefaults()

        // If no persisted data exists, load sample data
        if expenses.isEmpty {
            loadSampleData()
        }
    }

    private func saveDataToUserDefaults() {
        saveExpensesToUserDefaults()
        saveSettingsToUserDefaults()
    }

    private func loadExpensesFromUserDefaults() {
        if let data = UserDefaults.standard.data(forKey: "SavedExpenses") {
            do {
                expenses = try JSONDecoder().decode([ExpenseRecord].self, from: data)
                expenses.sort { $0.date > $1.date }
            } catch {
                print("Failed to load expenses: \(error)")
                expenses = []
            }
        }
    }

    private func saveExpensesToUserDefaults() {
        do {
            let data = try JSONEncoder().encode(expenses)
            UserDefaults.standard.set(data, forKey: "SavedExpenses")
        } catch {
            print("Failed to save expenses: \(error)")
        }
    }

    private func loadSettingsFromUserDefaults() {
        selectedCurrency = UserDefaults.standard.string(forKey: "SelectedCurrency") ?? "USD"
    }

    private func saveSettingsToUserDefaults() {
        UserDefaults.standard.set(selectedCurrency, forKey: "SelectedCurrency")
    }

    // MARK: - Expense Management
    func addExpense(_ expense: ExpenseRecord) {
        expenses.append(expense)
        expenses.sort { $0.date > $1.date }
        saveDataToUserDefaults()
    }

    func deleteExpense(withId id: String) {
        expenses.removeAll { $0.id == id }
        saveDataToUserDefaults()
    }

    func updateExpense(_ expense: ExpenseRecord) {
        if let index = expenses.firstIndex(where: { $0.id == expense.id }) {
            expenses[index] = expense
            expenses.sort { $0.date > $1.date }
            saveDataToUserDefaults()
        }
    }
    
    // MARK: - Statistics
    func getTotalSpending(for period: StatisticsPeriod = .monthly) -> Double {
        let filteredExpenses = getExpensesForPeriod(period)
        return filteredExpenses.reduce(0) { $0 + $1.amount }
    }
    
    func getDailyAverage(for period: StatisticsPeriod = .monthly) -> Double {
        let total = getTotalSpending(for: period)
        let days = getDaysInPeriod(period)
        return days > 0 ? total / Double(days) : 0
    }
    
    func getCategoryBreakdown() -> [CategoryData] {
        let categoryTotals = Dictionary(grouping: expenses) { $0.category }
            .mapValues { $0.reduce(0) { $0 + $1.amount } }
        
        let total = categoryTotals.values.reduce(0, +)
        
        return categoryTotals.map { category, amount in
            let percentage = total > 0 ? (amount / total) * 100 : 0
            let color = categories.first { $0.name == category }?.color ?? .gray
            return CategoryData(category: category, amount: amount, color: color, percentage: percentage)
        }.sorted { $0.amount > $1.amount }
    }
    
    func getMonthlyTrends() -> [ChartData] {
        let calendar = Calendar.current
        let now = Date()
        var monthlyData: [ChartData] = []
        
        for i in 0..<6 {
            let date = calendar.date(byAdding: .month, value: -i, to: now)!
            let monthExpenses = expenses.filter { calendar.isDate($0.date, equalTo: date, toGranularity: .month) }
            let total = monthExpenses.reduce(0) { $0 + $1.amount }
            
            let formatter = DateFormatter()
            formatter.dateFormat = "MMM"
            let monthName = formatter.string(from: date)
            
            monthlyData.append(ChartData(period: monthName, amount: total))
        }
        
        return monthlyData.reversed()
    }
    
    // MARK: - Helper Methods
    private func getExpensesForPeriod(_ period: StatisticsPeriod) -> [ExpenseRecord] {
        let calendar = Calendar.current
        let now = Date()
        
        switch period {
        case .monthly:
            return expenses.filter { calendar.isDate($0.date, equalTo: now, toGranularity: .month) }
        case .quarterly:
            let quarterStart = calendar.dateInterval(of: .quarter, for: now)?.start ?? now
            return expenses.filter { $0.date >= quarterStart }
        case .yearly:
            return expenses.filter { calendar.isDate($0.date, equalTo: now, toGranularity: .year) }
        }
    }
    
    private func getDaysInPeriod(_ period: StatisticsPeriod) -> Int {
        let calendar = Calendar.current
        let now = Date()
        
        switch period {
        case .monthly:
            return calendar.range(of: .day, in: .month, for: now)?.count ?? 30
        case .quarterly:
            return 90
        case .yearly:
            return calendar.range(of: .day, in: .year, for: now)?.count ?? 365
        }
    }
    
    // MARK: - Data Management
    func exportDataAsCSV() -> String {
        var csvString = "Date,Time,Category,Description,Amount,Currency\n"

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        for expense in expenses.sorted(by: { $0.date > $1.date }) {
            let dateString = dateFormatter.string(from: expense.date)
            let escapedDescription = expense.description.replacingOccurrences(of: "\"", with: "\"\"")
            csvString += "\(dateString),\(expense.time),\(expense.category),\"\(escapedDescription)\",\(expense.amount),\(selectedCurrency)\n"
        }

        return csvString
    }

    func clearAllData() {
        expenses.removeAll()
        UserDefaults.standard.removeObject(forKey: "SavedExpenses")
        UserDefaults.standard.removeObject(forKey: "SelectedCurrency")
        selectedCurrency = "USD"
    }

    func resetToSampleData() {
        clearAllData()
        loadSampleData()
        saveDataToUserDefaults()
    }

    // MARK: - Currency
    func getCurrencySymbol() -> String {
        switch selectedCurrency {
        case "USD": return "$"
        case "EUR": return "€"
        case "GBP": return "£"
        case "JPY": return "¥"
        case "SGD": return "S$"
        case "MYR": return "RM"
        case "THB": return "฿"
        default: return "$"
        }
    }
}

enum StatisticsPeriod: Int, CaseIterable {
    case monthly = 0
    case quarterly = 1
    case yearly = 2
    
    var displayName: String {
        switch self {
        case .monthly: return "Monthly"
        case .quarterly: return "Quarterly"
        case .yearly: return "Yearly"
        }
    }
}

// MARK: - Supporting Data Structures
struct CategoryData: Identifiable {
    let id = UUID()
    let category: String
    let amount: Double
    let color: Color
    let percentage: Double
}

struct ChartData: Identifiable {
    let id = UUID()
    let period: String
    let amount: Double
}

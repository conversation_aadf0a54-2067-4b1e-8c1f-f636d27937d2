//
//  NetworkService.swift
//  App-TalkTab
//
//  Created by z<PERSON><PERSON><PERSON> on 12/7/25.
//

import Foundation
import Combine

class NetworkService: ObservableObject {
    static let shared = NetworkService()
    
    // Base URL for the Flask backend
    private let baseURL = "http://127.0.0.1:5001/api"
    
    // Current user ID (in a real app, this would be managed by authentication)
    @Published var currentUserId: String?
    
    private init() {}
    
    // MARK: - User Management
    
    func createUser(username: String, email: String? = nil, currency: String = "USD") async throws -> User {
        let url = URL(string: "\(baseURL)/users")!
        
        let userData = CreateUserRequest(
            username: username,
            email: email,
            currency: currency
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONEncoder().encode(userData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw NetworkError.invalidResponse
        }
        
        let userResponse = try JSONDecoder().decode(CreateUserResponse.self, from: data)
        
        // Store the user ID for future requests
        DispatchQueue.main.async {
            self.currentUserId = userResponse.user.id
        }
        
        return userResponse.user
    }
    
    func getUser(userId: String) async throws -> User {
        let url = URL(string: "\(baseURL)/users/\(userId)")!
        
        let (data, response) = try await URLSession.shared.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }
        
        let userResponse = try JSONDecoder().decode(GetUserResponse.self, from: data)
        return userResponse.user
    }
    
    func updateUser(userId: String, currency: String) async throws -> User {
        let url = URL(string: "\(baseURL)/users/\(userId)")!
        
        let updateData = UpdateUserRequest(currency: currency)
        
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONEncoder().encode(updateData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }
        
        let userResponse = try JSONDecoder().decode(UpdateUserResponse.self, from: data)
        return userResponse.user
    }
    
    // MARK: - Categories
    
    func getCategories(userId: String) async throws -> [Category] {
        let url = URL(string: "\(baseURL)/users/\(userId)/categories")!
        
        let (data, response) = try await URLSession.shared.data(from: url)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }
        
        let categoriesResponse = try JSONDecoder().decode(CategoriesResponse.self, from: data)
        return categoriesResponse.categories
    }
    
    func createCategory(userId: String, name: String, icon: String, color: String) async throws -> Category {
        let url = URL(string: "\(baseURL)/users/\(userId)/categories")!
        
        let categoryData = CreateCategoryRequest(
            name: name,
            icon: icon,
            color: color
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONEncoder().encode(categoryData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw NetworkError.invalidResponse
        }
        
        let categoryResponse = try JSONDecoder().decode(CreateCategoryResponse.self, from: data)
        return categoryResponse.category
    }
    
    // MARK: - Expenses
    
    func getExpenses(userId: String, limit: Int? = nil, categoryId: String? = nil, startDate: String? = nil, endDate: String? = nil) async throws -> [Expense] {
        var urlComponents = URLComponents(string: "\(baseURL)/users/\(userId)/expenses")!
        
        var queryItems: [URLQueryItem] = []
        
        if let limit = limit {
            queryItems.append(URLQueryItem(name: "limit", value: String(limit)))
        }
        if let categoryId = categoryId {
            queryItems.append(URLQueryItem(name: "category_id", value: categoryId))
        }
        if let startDate = startDate {
            queryItems.append(URLQueryItem(name: "start_date", value: startDate))
        }
        if let endDate = endDate {
            queryItems.append(URLQueryItem(name: "end_date", value: endDate))
        }
        
        if !queryItems.isEmpty {
            urlComponents.queryItems = queryItems
        }
        
        let (data, response) = try await URLSession.shared.data(from: urlComponents.url!)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }
        
        let expensesResponse = try JSONDecoder().decode(ExpensesResponse.self, from: data)
        return expensesResponse.expenses
    }
    
    func createExpense(userId: String, amount: Double, description: String, categoryId: String, time: String, date: String? = nil) async throws -> Expense {
        let url = URL(string: "\(baseURL)/users/\(userId)/expenses")!
        
        let expenseData = CreateExpenseRequest(
            amount: amount,
            description: description,
            categoryId: categoryId,
            time: time,
            date: date
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONEncoder().encode(expenseData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw NetworkError.invalidResponse
        }
        
        let expenseResponse = try JSONDecoder().decode(CreateExpenseResponse.self, from: data)
        return expenseResponse.expense
    }
    
    func updateExpense(userId: String, expenseId: String, amount: Double?, description: String?, categoryId: String?, time: String?, date: String?) async throws -> Expense {
        let url = URL(string: "\(baseURL)/users/\(userId)/expenses/\(expenseId)")!
        
        let updateData = UpdateExpenseRequest(
            amount: amount,
            description: description,
            categoryId: categoryId,
            time: time,
            date: date
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONEncoder().encode(updateData)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }
        
        let expenseResponse = try JSONDecoder().decode(UpdateExpenseResponse.self, from: data)
        return expenseResponse.expense
    }
    
    func deleteExpense(userId: String, expenseId: String) async throws {
        let url = URL(string: "\(baseURL)/users/\(userId)/expenses/\(expenseId)")!
        
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NetworkError.invalidResponse
        }
    }
}

// MARK: - Network Error
enum NetworkError: Error {
    case invalidURL
    case invalidResponse
    case noData
    case decodingError
}

// MARK: - Request/Response Models
struct CreateUserRequest: Codable {
    let username: String
    let email: String?
    let currency: String
}

struct CreateUserResponse: Codable {
    let message: String
    let user: User
}

struct GetUserResponse: Codable {
    let user: User
}

struct UpdateUserRequest: Codable {
    let currency: String
}

struct UpdateUserResponse: Codable {
    let message: String
    let user: User
}

struct CategoriesResponse: Codable {
    let categories: [Category]
}

struct CreateCategoryRequest: Codable {
    let name: String
    let icon: String
    let color: String
}

struct CreateCategoryResponse: Codable {
    let message: String
    let category: Category
}

struct ExpensesResponse: Codable {
    let expenses: [Expense]
    let total: Int
}

struct CreateExpenseRequest: Codable {
    let amount: Double
    let description: String
    let categoryId: String
    let time: String
    let date: String?
    
    enum CodingKeys: String, CodingKey {
        case amount, description, time, date
        case categoryId = "category_id"
    }
}

struct CreateExpenseResponse: Codable {
    let message: String
    let expense: Expense
}

struct UpdateExpenseRequest: Codable {
    let amount: Double?
    let description: String?
    let categoryId: String?
    let time: String?
    let date: String?
    
    enum CodingKeys: String, CodingKey {
        case amount, description, time, date
        case categoryId = "category_id"
    }
}

struct UpdateExpenseResponse: Codable {
    let message: String
    let expense: Expense
}

//
//  NetworkDataManager.swift
//  App-TalkTab
//
//  Created by zcbob on 12/7/25.
//

import Foundation
import SwiftUI

@MainActor
class NetworkDataManager: ObservableObject {
    @Published var expenses: [ExpenseRecord] = []
    @Published var categories: [ExpenseCategory] = []
    @Published var selectedCurrency: String = "USD" {
        didSet {
            Task {
                await updateUserCurrency()
            }
        }
    }
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let networkService = NetworkService.shared
    private var currentUser: User?
    
    init() {
        // Initialize with empty data first to prevent crashes
        loadSampleData()

        // Then try to connect to backend
        Task {
            await initializeUser()
        }
    }

    private func loadSampleData() {
        // Load some sample data to prevent empty state crashes
        categories = [
            ExpenseCategory(id: "1", name: "Food & Dining", icon: "🍽️", color: .orange),
            ExpenseCategory(id: "2", name: "Transportation", icon: "🚗", color: .blue),
            ExpenseCategory(id: "3", name: "Shopping", icon: "🛍️", color: .green),
            ExpenseCategory(id: "4", name: "Entertainment", icon: "🎬", color: .purple)
        ]

        expenses = [
            ExpenseRecord(
                id: "sample1",
                category: "Food & Dining",
                amount: 25.50,
                description: "Sample lunch",
                time: "12:30",
                icon: "🍽️",
                color: .orange,
                date: Date(),
                currency: selectedCurrency
            )
        ]
    }
    
    // MARK: - User Management
    
    private func initializeUser() async {
        isLoading = true

        do {
            // Check if backend is available first
            let healthResponse = try await URLSession.shared.data(from: URL(string: "http://127.0.0.1:5001/api/health")!)
            print("✅ Backend health check successful")

            // For demo purposes, create a default user
            // In a real app, this would be handled by authentication
            let user = try await networkService.createUser(
                username: "ios_user_\(UUID().uuidString.prefix(8))",
                email: "<EMAIL>",
                currency: selectedCurrency
            )

            currentUser = user
            selectedCurrency = user.currency
            print("✅ User created: \(user.username)")

            await loadData()

        } catch {
            // If backend connection fails, continue with sample data
            print("⚠️ Backend not available, using sample data: \(error)")
            errorMessage = "Using offline mode - backend not available"

            // Keep the sample data that was loaded in init()
        }

        isLoading = false
    }
    
    private func updateUserCurrency() async {
        guard let userId = currentUser?.id else { return }
        
        do {
            let updatedUser = try await networkService.updateUser(userId: userId, currency: selectedCurrency)
            currentUser = updatedUser
        } catch {
            print("Failed to update currency: \(error)")
            errorMessage = "Failed to update currency: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Data Loading
    
    func loadData() async {
        guard let userId = currentUser?.id else {
            print("No user ID available, keeping sample data")
            return
        }

        isLoading = true

        do {
            // Load categories
            let backendCategories = try await networkService.getCategories(userId: userId)
            categories = backendCategories.map { ExpenseCategory(from: $0) }

            // Load expenses
            let backendExpenses = try await networkService.getExpenses(userId: userId)
            expenses = backendExpenses.map { ExpenseRecord(from: $0, currency: selectedCurrency) }

            errorMessage = nil
            print("Successfully loaded data from backend")
        } catch {
            print("Failed to load data from backend: \(error)")
            errorMessage = "Using offline data - backend connection failed"
            // Keep existing sample data instead of clearing it
        }

        isLoading = false
    }
    
    // MARK: - Expense Management
    
    func addExpense(_ expense: ExpenseRecord) {
        Task {
            await createExpense(expense)
        }
    }
    
    private func createExpense(_ expense: ExpenseRecord) async {
        guard let userId = currentUser?.id else { return }
        
        // Find the category ID
        guard let category = categories.first(where: { $0.name == expense.category }) else {
            errorMessage = "Category not found"
            return
        }
        
        isLoading = true
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            let backendExpense = try await networkService.createExpense(
                userId: userId,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            let newExpense = ExpenseRecord(from: backendExpense, currency: selectedCurrency)
            expenses.append(newExpense)
            expenses.sort { $0.date > $1.date }
            
            errorMessage = nil
        } catch {
            print("Failed to create expense: \(error)")
            errorMessage = "Failed to create expense: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func deleteExpense(withId id: String) {
        Task {
            await removeExpense(withId: id)
        }
    }
    
    private func removeExpense(withId id: String) async {
        guard let userId = currentUser?.id else { return }
        
        isLoading = true
        
        do {
            try await networkService.deleteExpense(userId: userId, expenseId: id)
            expenses.removeAll { $0.id == id }
            errorMessage = nil
        } catch {
            print("Failed to delete expense: \(error)")
            errorMessage = "Failed to delete expense: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func updateExpense(_ expense: ExpenseRecord) {
        Task {
            await modifyExpense(expense)
        }
    }
    
    private func modifyExpense(_ expense: ExpenseRecord) async {
        guard let userId = currentUser?.id else { return }
        
        // Find the category ID
        guard let category = categories.first(where: { $0.name == expense.category }) else {
            errorMessage = "Category not found"
            return
        }
        
        isLoading = true
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            let backendExpense = try await networkService.updateExpense(
                userId: userId,
                expenseId: expense.id,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            let updatedExpense = ExpenseRecord(from: backendExpense, currency: selectedCurrency)
            if let index = expenses.firstIndex(where: { $0.id == expense.id }) {
                expenses[index] = updatedExpense
                expenses.sort { $0.date > $1.date }
            }
            
            errorMessage = nil
        } catch {
            print("Failed to update expense: \(error)")
            errorMessage = "Failed to update expense: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Category Management
    
    func addCategory(name: String, icon: String, color: Color) {
        Task {
            await createCategory(name: name, icon: icon, color: color)
        }
    }
    
    private func createCategory(name: String, icon: String, color: Color) async {
        guard let userId = currentUser?.id else { return }
        
        isLoading = true
        
        do {
            let backendCategory = try await networkService.createCategory(
                userId: userId,
                name: name,
                icon: icon,
                color: color.toString()
            )
            
            let newCategory = ExpenseCategory(from: backendCategory)
            categories.append(newCategory)
            
            errorMessage = nil
        } catch {
            print("Failed to create category: \(error)")
            errorMessage = "Failed to create category: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Statistics and Utilities
    
    func getTotalExpenses() -> Double {
        expenses.reduce(0) { $0 + $1.amount }
    }
    
    func getTodayExpenses() -> [ExpenseRecord] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return expenses.filter { expense in
            expense.date >= today && expense.date < tomorrow
        }
    }
    
    func getTodayTotal() -> Double {
        getTodayExpenses().reduce(0) { $0 + $1.amount }
    }
    
    func getCurrencySymbol() -> String {
        switch selectedCurrency {
        case "USD": return "$"
        case "EUR": return "€"
        case "GBP": return "£"
        case "JPY": return "¥"
        case "SGD": return "S$"
        case "MYR": return "RM"
        case "THB": return "฿"
        case "CNY": return "¥"
        default: return "$"
        }
    }
    
    func refreshData() {
        Task {
            await loadData()
        }
    }
}

// MARK: - ExpenseRecord Extension for Backend Conversion
extension ExpenseRecord {
    init(from expense: Expense, currency: String = "USD") {
        self.init(
            id: expense.id,
            category: expense.category,
            amount: expense.amount,
            description: expense.description,
            time: expense.time,
            icon: expense.icon,
            color: Color.fromString(expense.color),
            date: expense.dateObject,
            currency: currency
        )
    }
}

// MARK: - ExpenseCategory Extension for Backend Conversion
extension ExpenseCategory {
    init(from category: Category) {
        self.init(
            id: category.id,
            name: category.name,
            icon: category.icon,
            color: Color.fromString(category.color),
            subcategories: [] // Backend doesn't support subcategories yet
        )
    }
}

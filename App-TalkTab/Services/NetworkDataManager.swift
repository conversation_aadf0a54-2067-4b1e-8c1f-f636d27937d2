//
//  NetworkDataManager.swift
//  App-TalkTab
//
//  Created by zcbob on 12/7/25.
//

import Foundation
import SwiftUI

@MainActor
class NetworkDataManager: ObservableObject {
    @Published var expenses: [ExpenseRecord] = []
    @Published var categories: [ExpenseCategory] = []
    @Published var selectedCurrency: String = "USD" {
        didSet {
            Task {
                await updateUserCurrency()
            }
        }
    }
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let networkService = NetworkService.shared
    private var currentUser: User?
    
    init() {
        NSLog("🚀 NetworkDataManager init started")

        // Initialize with safe defaults first
        self.expenses = []
        self.categories = []
        self.selectedCurrency = "USD"
        self.isLoading = false
        self.errorMessage = nil
        self.currentUser = nil

        NSLog("🔍 About to check for data migration...")

        // Check for existing local data and migrate if needed
        migrateLocalDataIfNeeded()

        NSLog("📊 After migration check - expenses count: \(expenses.count)")

        // Load sample data only if no local data exists
        if expenses.isEmpty {
            NSLog("📝 No expenses found, loading sample data...")
            loadSampleData()
        } else {
            NSLog("✅ Found existing expenses, skipping sample data")
        }

        // Then try to connect to backend in a safe way
        Task { @MainActor in
            await initializeUserSafely()
        }
    }

    private func loadSampleData() {
        // Load some sample expense data to prevent empty state crashes
        // Categories are loaded separately in loadDefaultCategories()
        expenses = [
            ExpenseRecord(
                id: "sample1",
                category: "Food & Dining",
                amount: 25.50,
                description: "Sample lunch",
                time: "12:30",
                icon: "🍽️",
                color: .orange,
                date: Date(),
                currency: selectedCurrency
            ),
            ExpenseRecord(
                id: "sample2",
                category: "Transportation",
                amount: 15.00,
                description: "Bus fare",
                time: "08:15",
                icon: "🚗",
                color: .blue,
                date: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                currency: selectedCurrency
            )
        ]
        print("✅ Sample expense data loaded successfully")
    }
    
    // MARK: - User Management
    
    private func initializeUserSafely() async {
        // Don't set loading state immediately to prevent UI issues
        await MainActor.run {
            isLoading = true
        }

        do {
            // Check if backend is available first with timeout
            let url = URL(string: "http://127.0.0.1:5001/api/health")!
            var request = URLRequest(url: url)
            request.timeoutInterval = 5.0 // 5 second timeout

            let (_, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                print("✅ Backend health check successful")

                // Try to create user
                let user = try await networkService.createUser(
                    username: "ios_user_\(UUID().uuidString.prefix(8))",
                    email: "<EMAIL>",
                    currency: selectedCurrency
                )

                await MainActor.run {
                    currentUser = user
                    selectedCurrency = user.currency
                }
                print("✅ User created: \(user.username)")

                await loadDataSafely()
            } else {
                throw NetworkError.invalidResponse
            }

        } catch {
            // If backend connection fails, continue with sample data
            print("⚠️ Backend not available, using sample data: \(error.localizedDescription)")
            await MainActor.run {
                errorMessage = "Using offline mode - backend not available"
            }
        }

        await MainActor.run {
            isLoading = false
        }
    }
    
    private func updateUserCurrency() async {
        guard let userId = currentUser?.id else { return }
        
        do {
            let updatedUser = try await networkService.updateUser(userId: userId, currency: selectedCurrency)
            currentUser = updatedUser
        } catch {
            print("Failed to update currency: \(error)")
            errorMessage = "Failed to update currency: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Data Loading
    
    func loadData() async {
        await loadDataSafely()
    }

    private func loadDataSafely() async {
        guard let userId = currentUser?.id else {
            print("No user ID available, keeping sample data")
            return
        }

        await MainActor.run {
            isLoading = true
        }

        do {
            // Load categories
            let backendCategories = try await networkService.getCategories(userId: userId)
            let backendExpenses = try await networkService.getExpenses(userId: userId)

            await MainActor.run {
                categories = backendCategories.map { ExpenseCategory(from: $0) }
                expenses = backendExpenses.map { ExpenseRecord(from: $0, currency: selectedCurrency) }
                errorMessage = nil
            }
            print("Successfully loaded data from backend")
        } catch {
            print("Failed to load data from backend: \(error)")
            await MainActor.run {
                errorMessage = "Using offline data - backend connection failed"
            }
            // Keep existing sample data instead of clearing it
        }

        await MainActor.run {
            isLoading = false
        }
    }
    
    // MARK: - Expense Management
    
    func addExpense(_ expense: ExpenseRecord) {
        Task {
            await createExpense(expense)
        }
    }
    
    private func createExpense(_ expense: ExpenseRecord) async {
        guard let userId = currentUser?.id else { return }
        
        // Find the category ID
        guard let category = categories.first(where: { $0.name == expense.category }) else {
            errorMessage = "Category not found"
            return
        }
        
        isLoading = true
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            let backendExpense = try await networkService.createExpense(
                userId: userId,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            let newExpense = ExpenseRecord(from: backendExpense, currency: selectedCurrency)
            expenses.append(newExpense)
            expenses.sort { $0.date > $1.date }
            
            errorMessage = nil
        } catch {
            print("Failed to create expense: \(error)")
            errorMessage = "Failed to create expense: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func deleteExpense(withId id: String) {
        Task {
            await removeExpense(withId: id)
        }
    }
    
    private func removeExpense(withId id: String) async {
        guard let userId = currentUser?.id else { return }
        
        isLoading = true
        
        do {
            try await networkService.deleteExpense(userId: userId, expenseId: id)
            expenses.removeAll { $0.id == id }
            errorMessage = nil
        } catch {
            print("Failed to delete expense: \(error)")
            errorMessage = "Failed to delete expense: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func updateExpense(_ expense: ExpenseRecord) {
        Task {
            await modifyExpense(expense)
        }
    }
    
    private func modifyExpense(_ expense: ExpenseRecord) async {
        guard let userId = currentUser?.id else { return }
        
        // Find the category ID
        guard let category = categories.first(where: { $0.name == expense.category }) else {
            errorMessage = "Category not found"
            return
        }
        
        isLoading = true
        
        do {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let dateString = dateFormatter.string(from: expense.date)
            
            let backendExpense = try await networkService.updateExpense(
                userId: userId,
                expenseId: expense.id,
                amount: expense.amount,
                description: expense.description,
                categoryId: category.id,
                time: expense.time,
                date: dateString
            )
            
            let updatedExpense = ExpenseRecord(from: backendExpense, currency: selectedCurrency)
            if let index = expenses.firstIndex(where: { $0.id == expense.id }) {
                expenses[index] = updatedExpense
                expenses.sort { $0.date > $1.date }
            }
            
            errorMessage = nil
        } catch {
            print("Failed to update expense: \(error)")
            errorMessage = "Failed to update expense: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Category Management
    
    func addCategory(name: String, icon: String, color: Color) {
        Task {
            await createCategory(name: name, icon: icon, color: color)
        }
    }
    
    private func createCategory(name: String, icon: String, color: Color) async {
        guard let userId = currentUser?.id else { return }
        
        isLoading = true
        
        do {
            let backendCategory = try await networkService.createCategory(
                userId: userId,
                name: name,
                icon: icon,
                color: color.toString()
            )
            
            let newCategory = ExpenseCategory(from: backendCategory)
            categories.append(newCategory)
            
            errorMessage = nil
        } catch {
            print("Failed to create category: \(error)")
            errorMessage = "Failed to create category: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Statistics and Utilities
    
    func getTotalExpenses() -> Double {
        expenses.reduce(0) { $0 + $1.amount }
    }
    
    func getTodayExpenses() -> [ExpenseRecord] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        return expenses.filter { expense in
            expense.date >= today && expense.date < tomorrow
        }
    }
    
    func getTodayTotal() -> Double {
        getTodayExpenses().reduce(0) { $0 + $1.amount }
    }
    
    func getCurrencySymbol() -> String {
        switch selectedCurrency {
        case "USD": return "$"
        case "EUR": return "€"
        case "GBP": return "£"
        case "JPY": return "¥"
        case "SGD": return "S$"
        case "MYR": return "RM"
        case "THB": return "฿"
        case "CNY": return "¥"
        default: return "$"
        }
    }
    
    // MARK: - Statistics Methods
    func getTotalSpending(for period: StatisticsPeriod = .monthly) -> Double {
        let filteredExpenses = getExpensesForPeriod(period)
        return filteredExpenses.reduce(0) { $0 + $1.amount }
    }

    func getDailyAverage(for period: StatisticsPeriod = .monthly) -> Double {
        let total = getTotalSpending(for: period)
        let days = getDaysInPeriod(period)
        return days > 0 ? total / Double(days) : 0
    }

    func getCategoryBreakdown() -> [CategoryData] {
        let categoryTotals = Dictionary(grouping: expenses) { $0.category }
            .mapValues { $0.reduce(0) { $0 + $1.amount } }

        let total = categoryTotals.values.reduce(0, +)

        return categoryTotals.map { category, amount in
            let percentage = total > 0 ? (amount / total) * 100 : 0
            let color = categories.first { $0.name == category }?.color ?? .gray
            return CategoryData(category: category, amount: amount, color: color, percentage: percentage)
        }.sorted { $0.amount > $1.amount }
    }

    func getMonthlyTrends() -> [ChartData] {
        let calendar = Calendar.current
        let now = Date()
        var monthlyData: [ChartData] = []

        for i in 0..<6 {
            let date = calendar.date(byAdding: .month, value: -i, to: now)!
            let monthExpenses = expenses.filter { calendar.isDate($0.date, equalTo: date, toGranularity: .month) }
            let total = monthExpenses.reduce(0) { $0 + $1.amount }

            let formatter = DateFormatter()
            formatter.dateFormat = "MMM"
            let monthName = formatter.string(from: date)

            monthlyData.append(ChartData(period: monthName, amount: total))
        }

        return monthlyData.reversed()
    }

    // MARK: - Helper Methods for Statistics
    private func getExpensesForPeriod(_ period: StatisticsPeriod) -> [ExpenseRecord] {
        let calendar = Calendar.current
        let now = Date()

        switch period {
        case .monthly:
            return expenses.filter { calendar.isDate($0.date, equalTo: now, toGranularity: .month) }
        case .quarterly:
            let quarterStart = calendar.dateInterval(of: .quarter, for: now)?.start ?? now
            return expenses.filter { $0.date >= quarterStart }
        case .yearly:
            return expenses.filter { calendar.isDate($0.date, equalTo: now, toGranularity: .year) }
        }
    }

    private func getDaysInPeriod(_ period: StatisticsPeriod) -> Int {
        let calendar = Calendar.current
        let now = Date()

        switch period {
        case .monthly:
            return calendar.range(of: .day, in: .month, for: now)?.count ?? 30
        case .quarterly:
            return 90
        case .yearly:
            return calendar.range(of: .day, in: .year, for: now)?.count ?? 365
        }
    }

    // MARK: - Data Management
    func exportDataAsCSV() -> String {
        var csvString = "Date,Time,Category,Description,Amount,Currency\n"

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        for expense in expenses.sorted(by: { $0.date > $1.date }) {
            let dateString = dateFormatter.string(from: expense.date)
            let escapedDescription = expense.description.replacingOccurrences(of: "\"", with: "\"\"")
            csvString += "\(dateString),\(expense.time),\(expense.category),\"\(escapedDescription)\",\(expense.amount),\(selectedCurrency)\n"
        }

        return csvString
    }

    func clearAllData() {
        expenses.removeAll()
        // For network data manager, we might want to also clear server data
        // but for now, just clear local data
    }

    func resetToSampleData() {
        clearAllData()
        loadSampleData()
        // For network data manager, we'll just load sample data locally
        // The sample data will be synced when the user adds new expenses
    }

    // MARK: - Data Migration
    private func migrateLocalDataIfNeeded() {
        NSLog("🔍 Starting migration check...")

        // Check what's in UserDefaults
        let savedExpensesExists = UserDefaults.standard.data(forKey: "SavedExpenses") != nil
        let savedCategoriesExists = UserDefaults.standard.data(forKey: "SavedCategories") != nil
        let selectedCurrencyExists = UserDefaults.standard.object(forKey: "SelectedCurrency") != nil

        NSLog("📋 UserDefaults check:")
        NSLog("   - SavedExpenses exists: \(savedExpensesExists)")
        NSLog("   - SavedCategories exists: \(savedCategoriesExists)")
        NSLog("   - SelectedCurrency exists: \(selectedCurrencyExists)")

        // Check if there's existing local data from ExpenseDataManager
        if let existingExpensesData = UserDefaults.standard.data(forKey: "SavedExpenses"),
           let existingExpenses = try? JSONDecoder().decode([ExpenseRecord].self, from: existingExpensesData) {

            NSLog("📦 Found \(existingExpenses.count) existing expense records, migrating...")
            self.expenses = existingExpenses

            // Clear the old data to prevent confusion
            UserDefaults.standard.removeObject(forKey: "SavedExpenses")
            NSLog("🗑️ Cleared old SavedExpenses data")
        } else {
            NSLog("❌ No existing expenses found or failed to decode")
        }

        // Migrate categories if they exist
        if let existingCategoriesData = UserDefaults.standard.data(forKey: "SavedCategories"),
           let existingCategories = try? JSONDecoder().decode([ExpenseCategory].self, from: existingCategoriesData) {

            NSLog("📦 Found \(existingCategories.count) existing categories, migrating...")
            self.categories = existingCategories

            // Clear the old data
            UserDefaults.standard.removeObject(forKey: "SavedCategories")
            NSLog("🗑️ Cleared old SavedCategories data")
        } else {
            NSLog("❌ No existing categories found, loading defaults...")
            // Load default categories if no existing ones
            loadDefaultCategories()
        }

        // Migrate selected currency
        if let existingCurrency = UserDefaults.standard.object(forKey: "SelectedCurrency") as? String {
            NSLog("📦 Found existing currency setting: \(existingCurrency), migrating...")
            self.selectedCurrency = existingCurrency

            // Clear the old data
            UserDefaults.standard.removeObject(forKey: "SelectedCurrency")
            NSLog("🗑️ Cleared old SelectedCurrency data")
        } else {
            NSLog("❌ No existing currency setting found")
        }

        // If we migrated any data, show a success message
        if !expenses.isEmpty {
            NSLog("✅ Successfully migrated \(expenses.count) expenses and \(categories.count) categories from local storage")
        } else {
            NSLog("ℹ️ No data was migrated")
        }
    }

    private func loadDefaultCategories() {
        categories = [
            ExpenseCategory(id: "1", name: "Food & Dining", icon: "🍽️", color: .orange, subcategories: []),
            ExpenseCategory(id: "2", name: "Transportation", icon: "🚗", color: .blue, subcategories: []),
            ExpenseCategory(id: "3", name: "Shopping", icon: "🛍️", color: .pink, subcategories: []),
            ExpenseCategory(id: "4", name: "Entertainment", icon: "🎬", color: .purple, subcategories: []),
            ExpenseCategory(id: "5", name: "Bills & Utilities", icon: "💡", color: .yellow, subcategories: []),
            ExpenseCategory(id: "6", name: "Healthcare", icon: "🏥", color: .red, subcategories: []),
            ExpenseCategory(id: "7", name: "Education", icon: "📚", color: .green, subcategories: []),
            ExpenseCategory(id: "8", name: "Travel", icon: "✈️", color: .cyan, subcategories: [])
        ]
    }

    func refreshData() {
        Task {
            await loadData()
        }
    }

    // MARK: - Testing Helper (for demonstration purposes)
    func createTestLocalData() {
        NSLog("🧪 Creating test local data...")

        // Create some test data in the old format to demonstrate migration
        let testExpenses = [
            ExpenseRecord(
                id: "test1",
                category: "Food & Dining",
                amount: 45.50,
                description: "Migrated lunch expense",
                time: "13:30",
                icon: "🍽️",
                color: .orange,
                date: Date(),
                currency: "USD"
            ),
            ExpenseRecord(
                id: "test2",
                category: "Transportation",
                amount: 12.00,
                description: "Migrated bus fare",
                time: "09:15",
                icon: "🚗",
                color: .blue,
                date: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                currency: "USD"
            )
        ]

        // Save in the old format
        do {
            let encoded = try JSONEncoder().encode(testExpenses)
            UserDefaults.standard.set(encoded, forKey: "SavedExpenses")
            UserDefaults.standard.set("EUR", forKey: "SelectedCurrency")
            UserDefaults.standard.synchronize() // Force immediate save

            NSLog("✅ Test data saved successfully:")
            NSLog("   - \(testExpenses.count) test expenses")
            NSLog("   - Currency set to EUR")

            // Verify the data was saved
            let savedData = UserDefaults.standard.data(forKey: "SavedExpenses")
            let savedCurrency = UserDefaults.standard.string(forKey: "SelectedCurrency")
            NSLog("🔍 Verification:")
            NSLog("   - SavedExpenses data size: \(savedData?.count ?? 0) bytes")
            NSLog("   - SavedCurrency: \(savedCurrency ?? "nil")")

        } catch {
            NSLog("❌ Failed to create test data: \(error)")
        }
    }
}

// MARK: - ExpenseRecord Extension for Backend Conversion
extension ExpenseRecord {
    init(from expense: Expense, currency: String = "USD") {
        self.init(
            id: expense.id,
            category: expense.category,
            amount: expense.amount,
            description: expense.description,
            time: expense.time,
            icon: expense.icon,
            color: Color.fromString(expense.color),
            date: expense.dateObject,
            currency: currency
        )
    }
}

// MARK: - ExpenseCategory Extension for Backend Conversion
extension ExpenseCategory {
    init(from category: Category) {
        self.init(
            id: category.id,
            name: category.name,
            icon: category.icon,
            color: Color.fromString(category.color),
            subcategories: [] // Backend doesn't support subcategories yet
        )
    }
}

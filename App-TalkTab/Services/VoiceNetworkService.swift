//
//  VoiceNetworkService.swift
//  App-TalkTab
//
//  Network service for uploading audio files and receiving voice recognition results
//

import Foundation
import SwiftUI

struct VoiceRecognitionResult {
    let id: String
    let amount: Double
    let currency: String
    let category: String
    let merchant: String
    let description: String
    let date: String
    let time: String
    let confidence: Double
    let recognizedText: String
    let rawText: String
}

struct VoiceUploadResponse: Codable {
    let id: String
    let status: String
    let result: VoiceResultData
}

struct VoiceResultData: Codable {
    let amount: Double
    let currency: String
    let category: String
    let merchant: String
    let description: String
    let date: String
    let time: String
    let confidence: Double
    let recognized_text: String
    let raw_text: String
}

@MainActor
class VoiceNetworkService: ObservableObject {
    
    @Published var isUploading = false
    @Published var uploadProgress: Double = 0.0
    @Published var errorMessage: String?
    
    private let baseURL: String
    private let session = URLSession.shared
    
    init() {
        // Use the same base URL as NetworkService
        // Use host IP for iOS simulator compatibility
        self.baseURL = ProcessInfo.processInfo.environment["API_BASE_URL"] ?? "http://**************:5002"
    }
    
    // MARK: - Voice Upload
    
    func uploadVoiceRecording(_ audioURL: URL) async -> VoiceRecognitionResult? {
        guard !isUploading else {
            NSLog("⚠️ Upload already in progress")
            return nil
        }
        
        isUploading = true
        uploadProgress = 0.0
        errorMessage = nil
        
        defer {
            isUploading = false
            uploadProgress = 0.0
        }
        
        do {
            // Prepare multipart form data
            let boundary = UUID().uuidString
            let url = URL(string: "\(baseURL)/api/upload")!
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
            
            // Create multipart body
            let httpBody = try createMultipartBody(audioURL: audioURL, boundary: boundary)
            request.httpBody = httpBody
            
            NSLog("🚀 Uploading voice recording to: \(url)")
            NSLog("📁 Audio file size: \(httpBody.count) bytes")
            
            // Upload with progress tracking
            let (data, response) = try await session.upload(for: request, from: httpBody)
            
            // Check HTTP response
            guard let httpResponse = response as? HTTPURLResponse else {
                throw VoiceNetworkError.invalidResponse
            }
            
            NSLog("📡 Upload response status: \(httpResponse.statusCode)")
            
            guard httpResponse.statusCode == 200 else {
                if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMsg = errorData["error"] as? String {
                    throw VoiceNetworkError.serverError(errorMsg)
                } else {
                    throw VoiceNetworkError.httpError(httpResponse.statusCode)
                }
            }
            
            // Parse response
            let uploadResponse = try JSONDecoder().decode(VoiceUploadResponse.self, from: data)
            
            NSLog("✅ Voice recognition completed successfully")
            NSLog("🎯 Recognized text: \(uploadResponse.result.recognized_text)")
            NSLog("💰 Extracted amount: \(uploadResponse.result.amount) \(uploadResponse.result.currency)")
            
            // Convert to VoiceRecognitionResult
            let result = VoiceRecognitionResult(
                id: uploadResponse.id,
                amount: uploadResponse.result.amount,
                currency: uploadResponse.result.currency,
                category: uploadResponse.result.category,
                merchant: uploadResponse.result.merchant,
                description: uploadResponse.result.description,
                date: uploadResponse.result.date,
                time: uploadResponse.result.time,
                confidence: uploadResponse.result.confidence,
                recognizedText: uploadResponse.result.recognized_text,
                rawText: uploadResponse.result.raw_text
            )
            
            return result
            
        } catch {
            NSLog("❌ Voice upload error: \(error)")
            errorMessage = error.localizedDescription
            return nil
        }
    }
    
    // MARK: - Multipart Form Data
    
    private func createMultipartBody(audioURL: URL, boundary: String) throws -> Data {
        var body = Data()
        
        // Add audio file
        let audioData = try Data(contentsOf: audioURL)
        let filename = audioURL.lastPathComponent
        let mimeType = getMimeType(for: audioURL.pathExtension)
        
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"audio\"; filename=\"\(filename)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: \(mimeType)\r\n\r\n".data(using: .utf8)!)
        body.append(audioData)
        body.append("\r\n".data(using: .utf8)!)
        
        // End boundary
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        return body
    }
    
    private func getMimeType(for fileExtension: String) -> String {
        switch fileExtension.lowercased() {
        case "wav":
            return "audio/wav"
        case "mp3":
            return "audio/mpeg"
        case "m4a":
            return "audio/mp4"
        case "aac":
            return "audio/aac"
        case "ogg":
            return "audio/ogg"
        default:
            return "audio/wav"
        }
    }
    
    // MARK: - Error Handling
    
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Error Types

enum VoiceNetworkError: LocalizedError {
    case invalidResponse
    case serverError(String)
    case httpError(Int)
    case encodingError
    case decodingError
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "Invalid server response"
        case .serverError(let message):
            return "Server error: \(message)"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .encodingError:
            return "Failed to encode request"
        case .decodingError:
            return "Failed to decode response"
        }
    }
}

// MARK: - URLSession Upload with Progress

extension URLSession {
    func upload(for request: URLRequest, from bodyData: Data) async throws -> (Data, URLResponse) {
        return try await withCheckedThrowingContinuation { continuation in
            let task = uploadTask(with: request, from: bodyData) { data, response, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let data = data, let response = response {
                    continuation.resume(returning: (data, response))
                } else {
                    continuation.resume(throwing: VoiceNetworkError.invalidResponse)
                }
            }
            task.resume()
        }
    }
}

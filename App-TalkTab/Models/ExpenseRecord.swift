//
//  ExpenseRecord.swift
//  App-TalkTab
//
//  Created by z<PERSON><PERSON><PERSON> on 11/7/25.
//

import SwiftUI
import Foundation

// MARK: - Backend API Models

struct User: Identifiable, Codable {
    let id: String
    let username: String
    let email: String?
    let currency: String
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id, username, email, currency
        case createdAt = "created_at"
    }
}

struct Category: Identifiable, Codable {
    let id: String
    let name: String
    let icon: String
    let color: String
    let createdAt: String

    enum CodingKeys: String, CodingKey {
        case id, name, icon, color
        case createdAt = "created_at"
    }

    var swiftUIColor: Color {
        Color.fromString(color)
    }
}

struct Expense: Identifiable, Codable {
    let id: String
    let amount: Double
    let description: String
    let category: String
    let categoryId: String
    let icon: String
    let color: String
    let date: String
    let time: String
    let createdAt: String
    let updatedAt: String

    enum CodingKeys: String, CodingKey {
        case id, amount, description, category, icon, color, date, time
        case categoryId = "category_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    var swiftUIColor: Color {
        Color.fromString(color)
    }

    var dateObject: Date {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.date(from: date) ?? Date()
    }
}

// MARK: - Legacy Models (for backward compatibility)

struct ExpenseRecord: Identifiable, Codable {
    let id: String
    var category: String
    var amount: Double
    var description: String
    var time: String
    var icon: String
    var color: Color
    var date: Date
    var currency: String
    
    init(id: String, category: String, amount: Double, description: String, time: String, icon: String, color: Color, date: Date = Date(), currency: String = "CNY") {
        self.id = id
        self.category = category
        self.amount = amount
        self.description = description
        self.time = time
        self.icon = icon
        self.color = color
        self.date = date
        self.currency = currency
    }
    
    // Custom coding for Color
    enum CodingKeys: String, CodingKey {
        case id, category, amount, description, time, icon, date, currency
        case colorData
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        category = try container.decode(String.self, forKey: .category)
        amount = try container.decode(Double.self, forKey: .amount)
        description = try container.decode(String.self, forKey: .description)
        time = try container.decode(String.self, forKey: .time)
        icon = try container.decode(String.self, forKey: .icon)
        date = try container.decode(Date.self, forKey: .date)
        currency = try container.decode(String.self, forKey: .currency)
        
        // Decode color from hex string
        let colorHex = try container.decode(String.self, forKey: .colorData)
        color = Color(hex: colorHex) ?? .blue
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(category, forKey: .category)
        try container.encode(amount, forKey: .amount)
        try container.encode(description, forKey: .description)
        try container.encode(time, forKey: .time)
        try container.encode(icon, forKey: .icon)
        try container.encode(date, forKey: .date)
        try container.encode(currency, forKey: .currency)
        try container.encode(color.toHex(), forKey: .colorData)
    }
}

struct ExpenseCategory: Identifiable, Codable {
    let id: String
    var name: String
    var icon: String
    var color: Color
    var subcategories: [String]
    
    init(id: String, name: String, icon: String, color: Color, subcategories: [String] = []) {
        self.id = id
        self.name = name
        self.icon = icon
        self.color = color
        self.subcategories = subcategories
    }
    
    // Custom coding for Color
    enum CodingKeys: String, CodingKey {
        case id, name, icon, subcategories
        case colorData
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        icon = try container.decode(String.self, forKey: .icon)
        subcategories = try container.decode([String].self, forKey: .subcategories)
        
        let colorHex = try container.decode(String.self, forKey: .colorData)
        color = Color(hex: colorHex) ?? .blue
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(icon, forKey: .icon)
        try container.encode(subcategories, forKey: .subcategories)
        try container.encode(color.toHex(), forKey: .colorData)
    }
}

// Color extension for hex conversion
extension Color {
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    func toHex() -> String {
        let uic = UIColor(self)
        guard let components = uic.cgColor.components, components.count >= 3 else {
            return "000000"
        }
        let r = Float(components[0])
        let g = Float(components[1])
        let b = Float(components[2])
        return String(format: "%02lX%02lX%02lX", lroundf(r * 255), lroundf(g * 255), lroundf(b * 255))
    }

    static func fromString(_ colorName: String) -> Color {
        switch colorName.lowercased() {
        case "red": return .red
        case "blue": return .blue
        case "green": return .green
        case "orange": return .orange
        case "yellow": return .yellow
        case "purple": return .purple
        case "pink": return .pink
        case "gray", "grey": return .gray
        case "black": return .black
        case "white": return .white
        case "brown": return .brown
        case "indigo": return .indigo
        default: return .gray
        }
    }

    func toString() -> String {
        // This is a simplified mapping - in a real app you might want a more robust solution
        if self == .red { return "red" }
        if self == .blue { return "blue" }
        if self == .green { return "green" }
        if self == .orange { return "orange" }
        if self == .yellow { return "yellow" }
        if self == .purple { return "purple" }
        if self == .pink { return "pink" }
        if self == .gray { return "gray" }
        if self == .black { return "black" }
        if self == .white { return "white" }
        if self == .brown { return "brown" }
        if self == .indigo { return "indigo" }
        return "gray"
    }
}

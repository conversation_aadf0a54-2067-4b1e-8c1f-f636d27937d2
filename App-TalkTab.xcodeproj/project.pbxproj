// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		4A7BB2052E212F0600B85DA1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4A7BB1EF2E212F0500B85DA1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4A7BB1F62E212F0500B85DA1;
			remoteInfo = "App-TalkTab";
		};
		4A7BB20F2E212F0600B85DA1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4A7BB1EF2E212F0500B85DA1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4A7BB1F62E212F0500B85DA1;
			remoteInfo = "App-TalkTab";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		4A7BB1F72E212F0500B85DA1 /* App-TalkTab.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "App-TalkTab.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		4A7BB2042E212F0600B85DA1 /* App-TalkTabTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "App-TalkTabTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		4A7BB20E2E212F0600B85DA1 /* App-TalkTabUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "App-TalkTabUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		4A7BB1F92E212F0500B85DA1 /* App-TalkTab */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "App-TalkTab";
			sourceTree = "<group>";
		};
		4A7BB2072E212F0600B85DA1 /* App-TalkTabTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "App-TalkTabTests";
			sourceTree = "<group>";
		};
		4A7BB2112E212F0600B85DA1 /* App-TalkTabUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "App-TalkTabUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		4A7BB1F42E212F0500B85DA1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A7BB2012E212F0600B85DA1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A7BB20B2E212F0600B85DA1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4A7BB1EE2E212F0500B85DA1 = {
			isa = PBXGroup;
			children = (
				4A7BB1F92E212F0500B85DA1 /* App-TalkTab */,
				4A7BB2072E212F0600B85DA1 /* App-TalkTabTests */,
				4A7BB2112E212F0600B85DA1 /* App-TalkTabUITests */,
				4A7BB1F82E212F0500B85DA1 /* Products */,
			);
			sourceTree = "<group>";
		};
		4A7BB1F82E212F0500B85DA1 /* Products */ = {
			isa = PBXGroup;
			children = (
				4A7BB1F72E212F0500B85DA1 /* App-TalkTab.app */,
				4A7BB2042E212F0600B85DA1 /* App-TalkTabTests.xctest */,
				4A7BB20E2E212F0600B85DA1 /* App-TalkTabUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4A7BB1F62E212F0500B85DA1 /* App-TalkTab */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4A7BB2182E212F0600B85DA1 /* Build configuration list for PBXNativeTarget "App-TalkTab" */;
			buildPhases = (
				4A7BB1F32E212F0500B85DA1 /* Sources */,
				4A7BB1F42E212F0500B85DA1 /* Frameworks */,
				4A7BB1F52E212F0500B85DA1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4A7BB1F92E212F0500B85DA1 /* App-TalkTab */,
			);
			name = "App-TalkTab";
			packageProductDependencies = (
			);
			productName = "App-TalkTab";
			productReference = 4A7BB1F72E212F0500B85DA1 /* App-TalkTab.app */;
			productType = "com.apple.product-type.application";
		};
		4A7BB2032E212F0600B85DA1 /* App-TalkTabTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4A7BB21B2E212F0600B85DA1 /* Build configuration list for PBXNativeTarget "App-TalkTabTests" */;
			buildPhases = (
				4A7BB2002E212F0600B85DA1 /* Sources */,
				4A7BB2012E212F0600B85DA1 /* Frameworks */,
				4A7BB2022E212F0600B85DA1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4A7BB2062E212F0600B85DA1 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				4A7BB2072E212F0600B85DA1 /* App-TalkTabTests */,
			);
			name = "App-TalkTabTests";
			packageProductDependencies = (
			);
			productName = "App-TalkTabTests";
			productReference = 4A7BB2042E212F0600B85DA1 /* App-TalkTabTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		4A7BB20D2E212F0600B85DA1 /* App-TalkTabUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4A7BB21E2E212F0600B85DA1 /* Build configuration list for PBXNativeTarget "App-TalkTabUITests" */;
			buildPhases = (
				4A7BB20A2E212F0600B85DA1 /* Sources */,
				4A7BB20B2E212F0600B85DA1 /* Frameworks */,
				4A7BB20C2E212F0600B85DA1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4A7BB2102E212F0600B85DA1 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				4A7BB2112E212F0600B85DA1 /* App-TalkTabUITests */,
			);
			name = "App-TalkTabUITests";
			packageProductDependencies = (
			);
			productName = "App-TalkTabUITests";
			productReference = 4A7BB20E2E212F0600B85DA1 /* App-TalkTabUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4A7BB1EF2E212F0500B85DA1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					4A7BB1F62E212F0500B85DA1 = {
						CreatedOnToolsVersion = 16.4;
					};
					4A7BB2032E212F0600B85DA1 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 4A7BB1F62E212F0500B85DA1;
					};
					4A7BB20D2E212F0600B85DA1 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 4A7BB1F62E212F0500B85DA1;
					};
				};
			};
			buildConfigurationList = 4A7BB1F22E212F0500B85DA1 /* Build configuration list for PBXProject "App-TalkTab" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 4A7BB1EE2E212F0500B85DA1;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 4A7BB1F82E212F0500B85DA1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4A7BB1F62E212F0500B85DA1 /* App-TalkTab */,
				4A7BB2032E212F0600B85DA1 /* App-TalkTabTests */,
				4A7BB20D2E212F0600B85DA1 /* App-TalkTabUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4A7BB1F52E212F0500B85DA1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A7BB2022E212F0600B85DA1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A7BB20C2E212F0600B85DA1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4A7BB1F32E212F0500B85DA1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A7BB2002E212F0600B85DA1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4A7BB20A2E212F0600B85DA1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4A7BB2062E212F0600B85DA1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4A7BB1F62E212F0500B85DA1 /* App-TalkTab */;
			targetProxy = 4A7BB2052E212F0600B85DA1 /* PBXContainerItemProxy */;
		};
		4A7BB2102E212F0600B85DA1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4A7BB1F62E212F0500B85DA1 /* App-TalkTab */;
			targetProxy = 4A7BB20F2E212F0600B85DA1 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		4A7BB2162E212F0600B85DA1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4A7BB2172E212F0600B85DA1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4A7BB2192E212F0600B85DA1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app needs microphone access to record voice input for expense tracking.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.bytelink.App-TalkTab";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4A7BB21A2E212F0600B85DA1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "This app needs microphone access to record voice input for expense tracking.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.bytelink.App-TalkTab";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		4A7BB21C2E212F0600B85DA1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.bytelink.App-TalkTabTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/App-TalkTab.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/App-TalkTab";
			};
			name = Debug;
		};
		4A7BB21D2E212F0600B85DA1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.bytelink.App-TalkTabTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/App-TalkTab.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/App-TalkTab";
			};
			name = Release;
		};
		4A7BB21F2E212F0600B85DA1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.bytelink.App-TalkTabUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "App-TalkTab";
			};
			name = Debug;
		};
		4A7BB2202E212F0600B85DA1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UGN6R5SLKP;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.bytelink.App-TalkTabUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "App-TalkTab";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4A7BB1F22E212F0500B85DA1 /* Build configuration list for PBXProject "App-TalkTab" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4A7BB2162E212F0600B85DA1 /* Debug */,
				4A7BB2172E212F0600B85DA1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4A7BB2182E212F0600B85DA1 /* Build configuration list for PBXNativeTarget "App-TalkTab" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4A7BB2192E212F0600B85DA1 /* Debug */,
				4A7BB21A2E212F0600B85DA1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4A7BB21B2E212F0600B85DA1 /* Build configuration list for PBXNativeTarget "App-TalkTabTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4A7BB21C2E212F0600B85DA1 /* Debug */,
				4A7BB21D2E212F0600B85DA1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4A7BB21E2E212F0600B85DA1 /* Build configuration list for PBXNativeTarget "App-TalkTabUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4A7BB21F2E212F0600B85DA1 /* Debug */,
				4A7BB2202E212F0600B85DA1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 4A7BB1EF2E212F0500B85DA1 /* Project object */;
}

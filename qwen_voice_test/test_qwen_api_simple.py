#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问语音识别API简化测试
不依赖pyaudio，使用下载的测试音频文件进行测试

API Key: sk-8fd0324578b24b558e520cb94696267a
模型: gummy-chat-v1
"""

import os
import sys
import time
import requests
import ssl
from pathlib import Path

# 修复SSL证书问题
try:
    import certifi
    os.environ['SSL_CERT_FILE'] = certifi.where()
    os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
except ImportError:
    pass

# 设置不验证SSL证书（仅用于测试）
ssl._create_default_https_context = ssl._create_unverified_context

try:
    import dashscope
    from dashscope.audio.asr import *
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请安装DashScope SDK: pip3 install dashscope")
    sys.exit(1)

# 设置API Key
dashscope.api_key = "sk-8fd0324578b24b558e520cb94696267a"

class SimpleVoiceTestCallback(TranslationRecognizerCallback):
    """简化的语音识别回调处理类"""
    
    def __init__(self):
        self.results = []
        self.is_complete = False
        self.error_message = None
        self.final_text = ""
        
    def on_open(self) -> None:
        print("🔗 API连接已建立")
        
    def on_close(self) -> None:
        print("🔌 API连接已关闭")
        
    def on_event(
        self,
        request_id,
        transcription_result: TranslationRecognizerCallback,
        translation_result: TranslationRecognizerCallback,
        usage,
    ) -> None:
        if transcription_result is not None:
            print(f"📝 识别结果: {transcription_result.text}")
            
            if transcription_result.is_sentence_end:
                self.final_text = transcription_result.text
                print(f"✅ 最终结果: {self.final_text}")
            
            self.results.append({
                'text': transcription_result.text,
                'is_final': transcription_result.is_sentence_end,
                'confidence': 0.95  # 模拟置信度
            })
                
    def on_error(self, message) -> None:
        print(f"❌ 识别错误: {message}")
        self.error_message = str(message)
        
    def on_complete(self) -> None:
        print("✅ 识别完成")
        self.is_complete = True

def download_test_audio():
    """下载测试音频文件"""
    test_audio_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/paraformer/hello_world_female2.wav"
    audio_file = "test_audio.wav"
    
    if not os.path.exists(audio_file):
        print(f"📥 正在下载测试音频文件...")
        try:
            response = requests.get(test_audio_url, timeout=30)
            response.raise_for_status()
            
            with open(audio_file, "wb") as f:
                f.write(response.content)
            print(f"✅ 测试音频文件下载完成: {audio_file}")
        except Exception as e:
            print(f"❌ 下载测试音频文件失败: {e}")
            return None
    else:
        print(f"📁 使用现有测试音频文件: {audio_file}")
        
    return audio_file

def test_api_connection():
    """测试API基本连接"""
    print("\n" + "="*50)
    print("🔗 测试1: API连接测试")
    print("="*50)
    
    try:
        # 创建一个简单的识别器实例来测试连接
        callback = SimpleVoiceTestCallback()
        recognizer = TranslationRecognizerChat(
            model="gummy-chat-v1",
            format="wav",
            sample_rate=16000,
            transcription_enabled=True,
            translation_enabled=False,
            callback=callback,
        )
        
        print("🚀 正在测试API连接...")
        recognizer.start()
        
        # 发送一个空的音频帧来测试连接
        empty_audio = b'\x00' * 1024
        recognizer.send_audio_frame(empty_audio)
        
        time.sleep(1)  # 等待响应
        recognizer.stop()
        
        print("✅ API连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def test_voice_recognition():
    """测试语音识别功能"""
    print("\n" + "="*50)
    print("🎤 测试2: 语音识别功能")
    print("="*50)
    
    # 下载测试音频
    audio_file = download_test_audio()
    if not audio_file:
        return False
        
    callback = SimpleVoiceTestCallback()
    
    # 创建识别器
    recognizer = TranslationRecognizerChat(
        model="gummy-chat-v1",
        format="wav",
        sample_rate=16000,
        transcription_enabled=True,
        translation_enabled=False,
        callback=callback,
    )
    
    try:
        recognizer.start()
        print("🚀 开始语音识别...")
        
        # 读取并发送音频数据
        with open(audio_file, 'rb') as f:
            chunk_size = 12800  # 每次读取12.8KB
            while True:
                audio_data = f.read(chunk_size)
                if not audio_data:
                    break
                    
                if not recognizer.send_audio_frame(audio_data):
                    print("🛑 检测到句子结束")
                    break
                    
                time.sleep(0.1)  # 模拟实时流
                
        recognizer.stop()
        
        # 等待完成
        timeout = 15
        start_time = time.time()
        while not callback.is_complete and not callback.error_message:
            if time.time() - start_time > timeout:
                print("⏰ 等待超时")
                break
            time.sleep(0.1)
            
        if callback.error_message:
            print(f"❌ 识别失败: {callback.error_message}")
            return False
            
        if callback.final_text:
            print(f"🎯 识别成功: {callback.final_text}")
            return True
        else:
            print("⚠️  未获得最终识别结果")
            return False
            
    except Exception as e:
        print(f"❌ 识别过程出错: {e}")
        return False

def test_expense_extraction():
    """测试支出信息提取"""
    print("\n" + "="*50)
    print("💰 测试3: 支出信息提取")
    print("="*50)
    
    # 模拟语音识别结果
    test_texts = [
        "我在麦当劳花了25块钱买午餐",
        "打车到机场花了80元",
        "星巴克咖啡35块",
        "超市购物120块钱",
        "I spent 15 dollars at McDonald's",
        "Grab ride cost 25 SGD"
    ]
    
    import re
    
    def extract_expense_info(text):
        """提取支出信息"""
        # 提取金额
        amount_patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:块钱|块|元|dollar|dollars|SGD)',
            r'花了\s*(\d+(?:\.\d+)?)',
            r'cost\s*(\d+(?:\.\d+)?)',
            r'spent\s*(\d+(?:\.\d+)?)',
        ]
        
        amount = None
        for pattern in amount_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                amount = float(match.group(1))
                break
        
        # 提取商家
        merchants = ['麦当劳', 'McDonald', '星巴克', 'Starbucks', 'Grab', '超市']
        merchant = None
        for m in merchants:
            if m.lower() in text.lower():
                merchant = m
                break
        
        return {
            'amount': amount,
            'merchant': merchant,
            'text': text
        }
    
    success_count = 0
    for i, text in enumerate(test_texts, 1):
        print(f"\n📋 测试 {i}: {text}")
        result = extract_expense_info(text)
        
        print(f"   💰 金额: {result['amount']}")
        print(f"   🏪 商家: {result['merchant']}")
        
        if result['amount'] is not None:
            success_count += 1
            print("   ✅ 提取成功")
        else:
            print("   ❌ 提取失败")
    
    print(f"\n📊 提取成功率: {success_count}/{len(test_texts)} ({success_count/len(test_texts)*100:.1f}%)")
    return success_count > len(test_texts) * 0.7  # 70%成功率

def main():
    """主测试函数"""
    print("🎯 通义千问语音识别API简化测试")
    print(f"🔑 API Key: {dashscope.api_key[:20]}...")
    print(f"🤖 模型: gummy-chat-v1")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 测试结果统计
    test_results = []
    
    # 测试1: API连接
    print("开始API连接测试...")
    result1 = test_api_connection()
    test_results.append(("API连接", result1))
    
    # 测试2: 语音识别
    print("开始语音识别测试...")
    result2 = test_voice_recognition()
    test_results.append(("语音识别", result2))
    
    # 测试3: 信息提取
    print("开始信息提取测试...")
    result3 = test_expense_extraction()
    test_results.append(("信息提取", result3))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    print(f"📈 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！")
        print("✅ 通义千问API对接成功")
        print("✅ 可以开始集成到应用中")
        return True
    else:
        print(f"\n⚠️  {total_count - success_count} 个测试失败")
        print("请检查网络连接和API配置")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        sys.exit(1)

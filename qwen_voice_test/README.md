# 通义千问语音识别API测试

本目录包含了Voice Accounting应用对接通义千问(Qwen Omni)语音识别API的测试文件。

## 📋 API信息

- **API Key**: `sk-8fd0324578b24b558e520cb94696267a`
- **SDK**: DashScope SDK
- **模型**: `gummy-chat-v1`
- **支持格式**: pcm, wav, mp3, opus, speex, aac, amr
- **采样率**: 16000Hz
- **音频时长限制**: 最大1分钟

## 🚀 快速开始

### 1. 安装依赖

```bash
pip3 install -r requirements.txt
```

### 2. 一键运行所有测试 (推荐)

```bash
python3 run_tests.py
```

这个脚本会自动运行所有测试并生成报告：
- 🔍 依赖检查
- 🧪 基础API测试
- 💰 支出场景测试
- 🎯 完整流程模拟
- 📊 生成测试报告

### 3. 单独运行测试

#### 基础API测试
```bash
python3 test_qwen_voice_api.py
```
- ✅ API连接测试
- ✅ 纯语音识别功能测试
- ✅ 语音识别+翻译功能测试

#### 支出场景测试
```bash
python3 test_expense_voice_recognition.py
```
- 💰 支出信息提取算法测试
- 🏪 商家识别测试
- 📊 分类推断测试

#### Voice Accounting模拟器
```bash
python3 voice_accounting_simulator.py
```
- 🎯 完整语音记账流程模拟
- 🌏 新加坡本地化支持
- 📊 统计报告生成

## 📁 文件列表

```
qwen_voice_test/
├── README.md                           # 说明文档
├── requirements.txt                    # Python依赖
├── run_tests.py                       # 一键测试运行器 ⭐
├── test_qwen_voice_api.py            # 基础API测试
├── test_expense_voice_recognition.py  # 支出场景测试
├── voice_accounting_simulator.py     # 完整流程模拟器
├── test_report.md                    # 自动生成的测试报告
└── expense_records.json              # 模拟生成的支出记录
```

## 📁 文件说明

### `run_tests.py` ⭐ (推荐使用)
一键测试运行器，自动执行所有测试并生成报告：

**功能**:
- 🔍 自动检查Python依赖
- 🧪 顺序执行所有测试脚本
- 📊 生成详细的测试报告
- ⏰ 超时保护和异常处理
- 💾 保存结果到Markdown文件

### `test_qwen_voice_api.py`
通用的通义千问语音识别API测试文件，验证基础功能：

**功能测试**:
- API连接状态
- 语音识别准确性
- 多语言翻译功能
- 实时流式处理

**测试流程**:
1. 下载测试音频文件
2. 创建识别器实例
3. 流式发送音频数据
4. 接收识别结果
5. 输出测试报告

### `test_expense_voice_recognition.py`
专门针对支出记录场景的语音识别测试：

**支出信息提取**:
- 💰 **金额识别**: "25块钱", "35元", "120块"
- 🏪 **商家识别**: "麦当劳", "星巴克", "超市"
- 📊 **分类推断**: 餐饮、交通、购物、娱乐等
- 📝 **描述保留**: 完整的语音识别文本

**测试场景**:
```
"我刚才在麦当劳花了25块钱买午餐"
→ 金额: 25 CNY, 商家: 麦当劳, 分类: 餐饮

"打车到机场花了80元"
→ 金额: 80 CNY, 商家: 出租车, 分类: 交通

"在星巴克买咖啡花了35块"
→ 金额: 35 CNY, 商家: 星巴克, 分类: 餐饮
```

### `voice_accounting_simulator.py`
完整的Voice Accounting应用流程模拟器：

**核心功能**:
- 🎯 **完整流程模拟**: 语音输入 → 识别 → 提取 → 结构化 → 存储
- 🌏 **新加坡本地化**: 支持SGD货币、本地商家识别
- 🏪 **智能商家识别**: Hawker Center、NTUC、Grab等
- 📊 **自动分类**: Food & Dining、Transportation、Shopping等
- 💾 **数据导出**: 生成JSON格式的支出记录

**支持场景**:
```
"I spent 15 dollars at McDonald's for lunch"
→ 15.0 SGD | McDonald'S | Food & Dining

"Grab ride to airport cost 25 SGD"
→ 25.0 SGD | Grab | Transportation

"我在亚坤花了8块钱买早餐"
→ 8.0 SGD | 亚坤 | Food & Dining
```

## 🧪 测试结果示例

### 基础API测试输出
```
🎯 通义千问语音识别API测试
📱 API Key: sk-8fd0324578b24b55...
🤖 模型: gummy-chat-v1

==================================================
🔗 测试0: API连接测试
==================================================
✅ API连接成功

==================================================
🎤 测试1: 纯语音识别功能
==================================================
📥 下载测试音频文件...
✅ 测试音频文件下载完成: test_audio.wav
🔗 连接已建立
🚀 开始语音识别...
📝 识别结果: 你好世界
✅ 识别完成

==================================================
📊 测试结果汇总
==================================================
API连接测试: ✅ 通过
纯语音识别: ✅ 通过
语音识别+翻译: ✅ 通过

🎯 总体结果: 3/3 测试通过
🎉 所有测试通过！API对接成功！
```

### 支出场景测试输出
```
🧪 支出场景语音识别测试
==================================================

📋 测试场景 1: 我刚才在麦当劳花了25块钱买午餐
🔍 分析文本: 我刚才在麦当劳花了25块钱买午餐
💰 提取的支出信息:
   金额: 25.0 CNY
   商家: 麦当劳
   分类: 餐饮
   描述: 我刚才在麦当劳花了25块钱买午餐
   ✅ 金额提取成功
```

## 🔧 配置说明

### API Key配置
API Key已经硬编码在测试文件中：
```python
dashscope.api_key = "sk-8fd0324578b24b558e520cb94696267a"
```

### 模型参数
```python
recognizer = TranslationRecognizerChat(
    model="gummy-chat-v1",           # 模型名称
    format="wav",                    # 音频格式
    sample_rate=16000,               # 采样率
    source_language="zh",            # 源语言(中文)
    transcription_enabled=True,      # 启用识别
    translation_enabled=False,       # 禁用翻译
    callback=callback,               # 回调处理
)
```

## 🚨 注意事项

1. **网络连接**: 确保网络连接正常，API需要访问阿里云服务
2. **音频格式**: 测试使用16kHz采样率的WAV格式
3. **时长限制**: 单次识别音频不能超过1分钟
4. **API配额**: 注意API调用次数和配额限制

## 🔄 下一步集成

测试通过后，可以将语音识别功能集成到iOS应用中：

1. **iOS端录音**: 使用AVFoundation录制音频
2. **音频上传**: 将录音文件上传到后端
3. **API调用**: 后端调用通义千问API进行识别
4. **结果解析**: 提取支出信息并返回给iOS端
5. **数据存储**: 将识别结果保存到数据库

## 📞 技术支持

如有问题，可以参考：
- [阿里云百炼文档](https://help.aliyun.com/zh/model-studio/)
- [DashScope SDK文档](https://help.aliyun.com/zh/model-studio/sentence-python-sdk)
- [GitHub示例代码](https://github.com/aliyun/alibabacloud-bailian-speech-demo)

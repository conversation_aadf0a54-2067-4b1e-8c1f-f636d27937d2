#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问语音识别API测试文件
测试Qwen Omni AI语音识别功能

API Key: sk-8fd0324578b24b558e520cb94696267a
模型: gummy-chat-v1
"""

import os
import sys
import time
import requests
from pathlib import Path

import dashscope
from dashscope.audio.asr import *

# 设置API Key
dashscope.api_key = "sk-8fd0324578b24b558e520cb94696267a"

class QwenVoiceTestCallback(TranslationRecognizerCallback):
    """语音识别回调处理类"""
    
    def __init__(self):
        self.results = []
        self.is_complete = False
        self.error_message = None
        
    def on_open(self) -> None:
        print("🔗 连接已建立")
        
    def on_close(self) -> None:
        print("🔌 连接已关闭")
        
    def on_event(
        self,
        request_id,
        transcription_result: TranscriptionResult,
        translation_result: TranslationResult,
        usage,
    ) -> None:
        print(f"📝 请求ID: {request_id}")
        
        # 处理识别结果
        if transcription_result is not None:
            print(f"🎯 识别结果: {transcription_result.text}")
            print(f"📊 句子ID: {transcription_result.sentence_id}")
            print(f"⏰ 开始时间: {transcription_result.begin_time}ms")
            print(f"⏰ 结束时间: {transcription_result.end_time}ms")
            print(f"✅ 是否完整句子: {transcription_result.is_sentence_end}")
            
            self.results.append({
                'type': 'transcription',
                'text': transcription_result.text,
                'sentence_id': transcription_result.sentence_id,
                'begin_time': transcription_result.begin_time,
                'end_time': transcription_result.end_time,
                'is_sentence_end': transcription_result.is_sentence_end
            })
            
        # 处理翻译结果
        if translation_result is not None:
            languages = translation_result.get_language_list()
            print(f"🌍 支持的翻译语言: {languages}")
            
            for lang in languages:
                translation = translation_result.get_translation(lang)
                print(f"🔄 翻译到{lang}: {translation.text}")
                
                self.results.append({
                    'type': 'translation',
                    'language': lang,
                    'text': translation.text,
                    'sentence_id': translation.sentence_id,
                    'begin_time': translation.begin_time,
                    'end_time': translation.end_time,
                    'is_sentence_end': translation.is_sentence_end
                })
        
        # 处理计费信息
        if usage:
            print(f"💰 使用量信息: {usage}")
            
    def on_error(self, message) -> None:
        print(f"❌ 错误: {message}")
        self.error_message = message
        
    def on_complete(self) -> None:
        print("✅ 识别完成")
        self.is_complete = True

def download_test_audio():
    """下载测试音频文件"""
    test_audio_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/paraformer/hello_world_female2.wav"
    audio_file = "test_audio.wav"
    
    if not os.path.exists(audio_file):
        print(f"📥 下载测试音频文件...")
        try:
            response = requests.get(test_audio_url)
            response.raise_for_status()
            
            with open(audio_file, "wb") as f:
                f.write(response.content)
            print(f"✅ 测试音频文件下载完成: {audio_file}")
        except Exception as e:
            print(f"❌ 下载测试音频文件失败: {e}")
            return None
    else:
        print(f"📁 使用现有测试音频文件: {audio_file}")
        
    return audio_file

def test_voice_recognition_only():
    """测试纯语音识别功能"""
    print("\n" + "="*50)
    print("🎤 测试1: 纯语音识别功能")
    print("="*50)
    
    audio_file = download_test_audio()
    if not audio_file:
        return False
        
    callback = QwenVoiceTestCallback()
    
    # 创建识别器 - 只启用识别功能
    recognizer = TranslationRecognizerChat(
        model="gummy-chat-v1",
        format="wav",
        sample_rate=16000,
        transcription_enabled=True,  # 启用识别
        translation_enabled=False,   # 禁用翻译
        callback=callback,
    )
    
    try:
        recognizer.start()
        print("🚀 开始语音识别...")
        
        # 读取并发送音频数据
        with open(audio_file, 'rb') as f:
            while True:
                audio_data = f.read(12800)  # 每次读取12.8KB
                if not audio_data:
                    break
                    
                if not recognizer.send_audio_frame(audio_data):
                    print("🛑 检测到句子结束，停止发送")
                    break
                    
                time.sleep(0.1)  # 模拟实时流
                
        recognizer.stop()
        
        # 等待完成
        timeout = 10
        start_time = time.time()
        while not callback.is_complete and not callback.error_message:
            if time.time() - start_time > timeout:
                print("⏰ 等待超时")
                break
            time.sleep(0.1)
            
        if callback.error_message:
            print(f"❌ 识别失败: {callback.error_message}")
            return False
            
        print(f"✅ 识别成功，共获得 {len(callback.results)} 个结果")
        return True
        
    except Exception as e:
        print(f"❌ 识别过程出错: {e}")
        return False

def test_voice_recognition_with_translation():
    """测试语音识别+翻译功能"""
    print("\n" + "="*50)
    print("🌍 测试2: 语音识别+翻译功能")
    print("="*50)
    
    audio_file = download_test_audio()
    if not audio_file:
        return False
        
    callback = QwenVoiceTestCallback()
    
    # 创建识别器 - 启用识别和翻译功能
    recognizer = TranslationRecognizerChat(
        model="gummy-chat-v1",
        format="wav",
        sample_rate=16000,
        source_language="zh",  # 源语言为中文
        transcription_enabled=True,   # 启用识别
        translation_enabled=True,     # 启用翻译
        translation_target_languages=["en"],  # 翻译为英文
        callback=callback,
    )
    
    try:
        recognizer.start()
        print("🚀 开始语音识别和翻译...")
        
        # 读取并发送音频数据
        with open(audio_file, 'rb') as f:
            while True:
                audio_data = f.read(12800)  # 每次读取12.8KB
                if not audio_data:
                    break
                    
                if not recognizer.send_audio_frame(audio_data):
                    print("🛑 检测到句子结束，停止发送")
                    break
                    
                time.sleep(0.1)  # 模拟实时流
                
        recognizer.stop()
        
        # 等待完成
        timeout = 10
        start_time = time.time()
        while not callback.is_complete and not callback.error_message:
            if time.time() - start_time > timeout:
                print("⏰ 等待超时")
                break
            time.sleep(0.1)
            
        if callback.error_message:
            print(f"❌ 识别翻译失败: {callback.error_message}")
            return False
            
        print(f"✅ 识别翻译成功，共获得 {len(callback.results)} 个结果")
        return True
        
    except Exception as e:
        print(f"❌ 识别翻译过程出错: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n" + "="*50)
    print("🔗 测试0: API连接测试")
    print("="*50)
    
    try:
        # 简单的连接测试
        callback = QwenVoiceTestCallback()
        recognizer = TranslationRecognizerChat(
            model="gummy-chat-v1",
            format="pcm",
            sample_rate=16000,
            transcription_enabled=True,
            callback=callback,
        )
        
        recognizer.start()
        print("✅ API连接成功")
        recognizer.stop()
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 通义千问语音识别API测试")
    print(f"📱 API Key: {dashscope.api_key[:20]}...")
    print(f"🤖 模型: gummy-chat-v1")
    
    # 测试结果统计
    test_results = []
    
    # 测试1: API连接
    result1 = test_api_connection()
    test_results.append(("API连接测试", result1))
    
    # 测试2: 纯语音识别
    result2 = test_voice_recognition_only()
    test_results.append(("纯语音识别", result2))
    
    # 测试3: 语音识别+翻译
    result3 = test_voice_recognition_with_translation()
    test_results.append(("语音识别+翻译", result3))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！API对接成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")
        return False

if __name__ == "__main__":
    main()

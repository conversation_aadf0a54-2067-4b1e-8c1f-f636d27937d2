#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice Accounting 应用模拟器
模拟完整的语音记账流程，包括语音识别、信息提取、数据结构化

这个文件模拟了iOS应用中的完整流程：
1. 用户语音输入 → 2. 语音识别 → 3. 信息提取 → 4. 数据结构化 → 5. 存储
"""

import json
import time
import re
from datetime import datetime
from typing import Dict, List, Optional

import dashscope
from dashscope.audio.asr import *

# 设置API Key
dashscope.api_key = "sk-8fd0324578b24b558e520cb94696267a"

class ExpenseRecord:
    """支出记录数据模型"""
    
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', f"exp_{int(time.time())}")
        self.amount = kwargs.get('amount', 0.0)
        self.currency = kwargs.get('currency', 'SGD')  # 默认新加坡元
        self.category = kwargs.get('category', 'Other')
        self.merchant = kwargs.get('merchant', '')
        self.description = kwargs.get('description', '')
        self.date = kwargs.get('date', datetime.now().strftime('%Y-%m-%d'))
        self.time = kwargs.get('time', datetime.now().strftime('%H:%M:%S'))
        self.source = kwargs.get('source', 'voice')
        self.confidence = kwargs.get('confidence', 0.0)
        self.raw_text = kwargs.get('raw_text', '')
        
    def to_dict(self):
        return {
            'id': self.id,
            'amount': self.amount,
            'currency': self.currency,
            'category': self.category,
            'merchant': self.merchant,
            'description': self.description,
            'date': self.date,
            'time': self.time,
            'source': self.source,
            'confidence': self.confidence,
            'raw_text': self.raw_text
        }
        
    def __str__(self):
        return f"💰 {self.amount} {self.currency} | 🏪 {self.merchant} | 📊 {self.category} | 📝 {self.description}"

class VoiceAccountingProcessor:
    """语音记账处理器"""
    
    def __init__(self):
        # 新加坡本地化配置
        self.default_currency = 'SGD'
        self.currency_patterns = {
            'SGD': ['dollar', 'dollars', '块', '元', 'sgd'],
            'USD': ['usd', 'us dollar', '美元'],
            'MYR': ['myr', 'ringgit', '马币'],
            'CNY': ['rmb', '人民币', '块钱'],
            'THB': ['thb', 'baht', '泰铢'],
        }
        
        # 新加坡常见商家
        self.singapore_merchants = {
            # 餐饮
            'Food & Dining': [
                'hawker center', 'hawker', 'food court', 'kopitiam',
                'mcdonald', 'kfc', 'burger king', 'subway',
                'starbucks', 'coffee bean', 'ya kun', 'toast box',
                'din tai fung', 'crystal jade', 'jumbo seafood',
                '麦当劳', '肯德基', '星巴克', '亚坤', '土司工坊'
            ],
            # 交通
            'Transportation': [
                'grab', 'gojek', 'taxi', 'uber', 'mrt', 'bus',
                'ez-link', 'nets flashpay', 'comfort', 'citycab',
                '地铁', '公交', '打车', '出租车'
            ],
            # 购物
            'Shopping': [
                'ntuc', 'cold storage', 'giant', 'sheng siong',
                'mustafa', 'uniqlo', 'h&m', 'zara',
                'ion orchard', 'vivocity', 'marina bay sands',
                '超市', '商场', '购物中心'
            ],
            # 娱乐
            'Entertainment': [
                'cinema', 'ktv', 'golden village', 'cathay',
                'sentosa', 'universal studios', 'zoo',
                '电影院', 'KTV', '圣淘沙', '动物园'
            ]
        }
        
    def extract_amount(self, text: str) -> tuple:
        """提取金额和货币"""
        # 金额提取模式 (支持小数)
        amount_patterns = [
            r'(\d+(?:\.\d{1,2})?)\s*(?:dollar|dollars|块|元|sgd)',
            r'花了\s*(\d+(?:\.\d{1,2})?)',
            r'(\d+(?:\.\d{1,2})?)\s*钱',
            r'cost\s*(\d+(?:\.\d{1,2})?)',
            r'paid\s*(\d+(?:\.\d{1,2})?)',
            r'(\d+(?:\.\d{1,2})?)\s*(?:SGD|USD|MYR|CNY|THB)',
        ]
        
        amount = None
        currency = self.default_currency
        
        for pattern in amount_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                amount = float(match.group(1))
                break
                
        # 检测货币类型
        text_lower = text.lower()
        for curr, keywords in self.currency_patterns.items():
            for keyword in keywords:
                if keyword in text_lower:
                    currency = curr
                    break
                    
        return amount, currency
        
    def extract_merchant_and_category(self, text: str) -> tuple:
        """提取商家和分类"""
        text_lower = text.lower()
        
        for category, merchants in self.singapore_merchants.items():
            for merchant in merchants:
                if merchant in text_lower:
                    return merchant.title(), category
                    
        return '', 'Other'
        
    def process_voice_text(self, recognized_text: str, confidence: float = 0.95) -> ExpenseRecord:
        """处理语音识别文本，生成支出记录"""
        
        # 提取金额和货币
        amount, currency = self.extract_amount(recognized_text)
        
        # 提取商家和分类
        merchant, category = self.extract_merchant_and_category(recognized_text)
        
        # 创建支出记录
        expense = ExpenseRecord(
            amount=amount or 0.0,
            currency=currency,
            category=category,
            merchant=merchant,
            description=recognized_text,
            confidence=confidence,
            raw_text=recognized_text
        )
        
        return expense

class VoiceAccountingCallback(TranslationRecognizerCallback):
    """语音记账回调处理器"""
    
    def __init__(self, processor: VoiceAccountingProcessor):
        self.processor = processor
        self.final_text = ""
        self.is_complete = False
        self.error_message = None
        self.expense_record = None
        
    def on_open(self) -> None:
        print("🎤 开始语音记账...")
        
    def on_close(self) -> None:
        print("🔌 语音识别结束")
        
    def on_event(self, request_id, transcription_result, translation_result, usage):
        if transcription_result is not None:
            print(f"📝 识别中: {transcription_result.text}")
            
            # 如果是完整句子，处理并生成支出记录
            if transcription_result.is_sentence_end:
                self.final_text = transcription_result.text
                print(f"✅ 最终识别: {self.final_text}")
                
                # 处理语音文本，生成支出记录
                self.expense_record = self.processor.process_voice_text(
                    self.final_text, 
                    confidence=0.95
                )
                
    def on_error(self, message) -> None:
        print(f"❌ 识别错误: {message}")
        self.error_message = message
        
    def on_complete(self) -> None:
        print("🎯 语音记账完成")
        self.is_complete = True

def simulate_voice_accounting():
    """模拟语音记账流程"""
    
    print("🎯 Voice Accounting 应用模拟器")
    print("="*50)
    
    # 创建处理器
    processor = VoiceAccountingProcessor()
    
    # 模拟不同的语音输入场景
    test_scenarios = [
        "I spent 15 dollars at McDonald's for lunch",
        "Grab ride to airport cost 25 SGD", 
        "Coffee at Starbucks 6.50 dollars",
        "NTUC grocery shopping 45 dollars",
        "Movie ticket at Golden Village 12 SGD",
        "我在亚坤花了8块钱买早餐",
        "地铁卡充值20新币",
        "在乌节路购物花了150块"
    ]
    
    expense_records = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📱 场景 {i}: 用户语音输入")
        print(f"🗣️  \"{scenario}\"")
        
        # 模拟语音识别过程
        print("🔄 正在识别...")
        time.sleep(0.5)  # 模拟识别延迟
        
        # 处理识别结果
        expense = processor.process_voice_text(scenario)
        expense_records.append(expense)
        
        # 显示结果
        print("💳 生成支出记录:")
        print(f"   {expense}")
        print(f"   置信度: {expense.confidence:.1%}")
        
        # 验证关键信息提取
        validation = []
        if expense.amount > 0:
            validation.append("✅ 金额")
        else:
            validation.append("❌ 金额")
            
        if expense.merchant:
            validation.append("✅ 商家")
        else:
            validation.append("❌ 商家")
            
        if expense.category != 'Other':
            validation.append("✅ 分类")
        else:
            validation.append("❌ 分类")
            
        print(f"   验证: {' | '.join(validation)}")
    
    # 生成统计报告
    print("\n" + "="*50)
    print("📊 记账统计报告")
    print("="*50)
    
    total_amount = sum(r.amount for r in expense_records)
    print(f"💰 总支出: {total_amount:.2f} SGD")
    
    # 按分类统计
    category_stats = {}
    for record in expense_records:
        category = record.category
        if category not in category_stats:
            category_stats[category] = {'count': 0, 'amount': 0.0}
        category_stats[category]['count'] += 1
        category_stats[category]['amount'] += record.amount
    
    print("\n📊 分类统计:")
    for category, stats in category_stats.items():
        print(f"   {category}: {stats['count']}笔, {stats['amount']:.2f} SGD")
    
    # 保存记录到JSON文件
    records_data = [record.to_dict() for record in expense_records]
    with open('expense_records.json', 'w', encoding='utf-8') as f:
        json.dump(records_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 记录已保存到: expense_records.json")
    
    return expense_records

def main():
    """主函数"""
    print("🚀 Voice Accounting - 语音记账模拟器")
    print(f"🌏 地区: 新加坡 (Singapore)")
    print(f"💱 默认货币: SGD")
    print(f"🤖 AI模型: Qwen Omni (gummy-chat-v1)")
    
    # 运行模拟
    records = simulate_voice_accounting()
    
    print("\n" + "="*50)
    print("🎉 模拟完成")
    print("="*50)
    print(f"✅ 成功处理 {len(records)} 条语音记录")
    print("✅ 支持中英文混合输入")
    print("✅ 支持新加坡本地商家识别")
    print("✅ 支持多种货币单位")
    
    print("\n🔄 下一步集成到iOS应用:")
    print("1. 实现iOS端语音录制功能")
    print("2. 集成通义千问API到后端")
    print("3. 优化信息提取算法")
    print("4. 添加用户确认和编辑功能")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SSL证书问题的测试脚本
"""

import ssl
import certifi
import os

def fix_ssl_certificates():
    """修复SSL证书问题"""
    print("🔧 尝试修复SSL证书问题...")
    
    # 方法1: 设置证书路径
    try:
        import certifi
        os.environ['SSL_CERT_FILE'] = certifi.where()
        os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
        print(f"✅ 设置证书路径: {certifi.where()}")
    except ImportError:
        print("⚠️  certifi未安装，尝试其他方法")
    
    # 方法2: 创建不验证SSL的上下文
    try:
        ssl._create_default_https_context = ssl._create_unverified_context
        print("✅ 设置为不验证SSL证书")
    except Exception as e:
        print(f"⚠️  SSL上下文设置失败: {e}")

def test_network_connection():
    """测试网络连接"""
    import requests
    
    print("\n🌐 测试网络连接...")
    
    # 测试基本网络连接
    try:
        response = requests.get("https://www.baidu.com", timeout=10, verify=False)
        print("✅ 基本网络连接正常")
    except Exception as e:
        print(f"❌ 基本网络连接失败: {e}")
        return False
    
    # 测试阿里云连接
    try:
        response = requests.get("https://dashscope.aliyuncs.com", timeout=10, verify=False)
        print("✅ 阿里云服务连接正常")
        return True
    except Exception as e:
        print(f"❌ 阿里云服务连接失败: {e}")
        return False

def main():
    print("🔧 SSL证书问题诊断和修复")
    print("="*40)
    
    # 修复SSL证书
    fix_ssl_certificates()
    
    # 测试网络连接
    network_ok = test_network_connection()
    
    if network_ok:
        print("\n✅ 网络连接正常，可以继续API测试")
        return True
    else:
        print("\n❌ 网络连接有问题，请检查网络设置")
        return False

if __name__ == "__main__":
    main()

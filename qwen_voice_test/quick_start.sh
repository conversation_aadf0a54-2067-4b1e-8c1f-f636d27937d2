#!/bin/bash

# 通义千问语音识别API测试 - 快速启动脚本

echo "🎯 通义千问语音识别API测试 - 快速启动"
echo "=================================================="

# 检查Python版本
echo "🐍 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装，请先安装Python 3.8+"
    exit 1
fi

# 安装依赖
echo ""
echo "📦 安装依赖包..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo ""
echo "✅ 环境准备完成"

# 运行测试
echo ""
echo "🚀 开始运行简化测试..."
echo "=================================================="
python3 test_qwen_api_simple.py

echo ""
echo "🎉 测试完成！"
echo "📄 详细报告请查看: test_report.md"
echo "💾 支出记录样本: expense_records.json"

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支出记录语音识别测试
专门测试通义千问在支出记录场景下的语音识别效果

使用场景：
- 用户说："我刚才在麦当劳花了25块钱买午餐"
- 期望识别出：金额(25)、商家(麦当劳)、分类(餐饮)、描述(午餐)
"""

import os
import json
import re
import dashscope
from dashscope.audio.asr import *

# 设置API Key
dashscope.api_key = "sk-8fd0324578b24b558e520cb94696267a"

class ExpenseVoiceCallback(TranslationRecognizerCallback):
    """支出记录语音识别回调"""
    
    def __init__(self):
        self.final_text = ""
        self.is_complete = False
        self.error_message = None
        
    def on_open(self) -> None:
        print("🎤 开始录音识别...")
        
    def on_close(self) -> None:
        print("🔌 识别结束")
        
    def on_event(self, request_id, transcription_result, translation_result, usage):
        if transcription_result is not None:
            print(f"📝 实时识别: {transcription_result.text}")
            
            # 如果是完整句子，保存最终结果
            if transcription_result.is_sentence_end:
                self.final_text = transcription_result.text
                print(f"✅ 最终识别结果: {self.final_text}")
                
    def on_error(self, message) -> None:
        print(f"❌ 识别错误: {message}")
        self.error_message = message
        
    def on_complete(self) -> None:
        print("🎯 识别完成")
        self.is_complete = True

def extract_expense_info(text):
    """从识别文本中提取支出信息"""
    print(f"\n🔍 分析文本: {text}")
    
    expense_info = {
        'amount': None,
        'merchant': None,
        'category': None,
        'description': text,
        'currency': 'CNY'
    }
    
    # 提取金额 (支持: 25块、25元、25块钱、25.5元等)
    amount_patterns = [
        r'(\d+(?:\.\d+)?)\s*(?:块钱|块|元|刀)',
        r'花了\s*(\d+(?:\.\d+)?)',
        r'(\d+(?:\.\d+)?)\s*钱',
        r'(\d+(?:\.\d+)?)\s*(?:SGD|USD|MYR|THB|IDR|PHP)',
    ]
    
    for pattern in amount_patterns:
        match = re.search(pattern, text)
        if match:
            expense_info['amount'] = float(match.group(1))
            break
    
    # 提取商家/地点
    merchant_keywords = [
        '麦当劳', '肯德基', '星巴克', '必胜客', '汉堡王',
        '海底捞', '呷哺呷哺', '西贝', '外婆家', '绿茶',
        '7-11', '全家', '罗森', '便利店',
        '超市', '沃尔玛', '家乐福', '大润发',
        'Grab', 'Uber', '滴滴', '出租车',
        '电影院', '万达', 'KTV', '健身房'
    ]
    
    for keyword in merchant_keywords:
        if keyword in text:
            expense_info['merchant'] = keyword
            break
    
    # 根据关键词推断分类
    category_mapping = {
        '餐饮': ['麦当劳', '肯德基', '星巴克', '必胜客', '汉堡王', '海底捞', '呷哺呷哺', '西贝', '外婆家', '绿茶', '午餐', '晚餐', '早餐', '吃饭', '喝咖啡', '喝茶'],
        '交通': ['Grab', 'Uber', '滴滴', '出租车', '地铁', '公交', '打车', '坐车'],
        '购物': ['超市', '沃尔玛', '家乐福', '大润发', '7-11', '全家', '罗森', '便利店', '买', '购买'],
        '娱乐': ['电影院', '万达', 'KTV', '唱歌', '看电影', '游戏'],
        '健康': ['健身房', '医院', '药店', '体检', '看病'],
        '其他': []
    }
    
    for category, keywords in category_mapping.items():
        for keyword in keywords:
            if keyword in text:
                expense_info['category'] = category
                break
        if expense_info['category']:
            break
    
    if not expense_info['category']:
        expense_info['category'] = '其他'
    
    return expense_info

def test_expense_scenarios():
    """测试不同的支出场景"""
    
    # 模拟不同的支出语音输入
    test_scenarios = [
        "我刚才在麦当劳花了25块钱买午餐",
        "打车到机场花了80元",
        "在星巴克买咖啡花了35块",
        "超市购物花了120块钱",
        "看电影票价45元",
        "健身房月卡300块",
        "便利店买水花了5块钱"
    ]
    
    print("🧪 支出场景语音识别测试")
    print("="*50)
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 测试场景 {i}: {scenario}")
        
        # 模拟语音识别结果 (实际应用中这里会是真实的语音识别)
        recognized_text = scenario
        
        # 提取支出信息
        expense_info = extract_expense_info(recognized_text)
        
        # 输出结果
        print("💰 提取的支出信息:")
        print(f"   金额: {expense_info['amount']} {expense_info['currency']}")
        print(f"   商家: {expense_info['merchant']}")
        print(f"   分类: {expense_info['category']}")
        print(f"   描述: {expense_info['description']}")
        
        # 验证提取效果
        if expense_info['amount'] is not None:
            print("   ✅ 金额提取成功")
        else:
            print("   ❌ 金额提取失败")

def test_real_voice_recognition():
    """测试真实语音识别"""
    print("\n🎤 真实语音识别测试")
    print("="*50)
    print("请准备一个包含支出信息的音频文件进行测试...")
    
    # 这里可以添加真实的音频文件测试
    # 由于没有实际音频文件，我们跳过这个测试
    print("⚠️  跳过真实语音测试 (需要音频文件)")

def create_expense_record(expense_info):
    """根据提取的信息创建支出记录"""
    
    # 模拟创建支出记录的数据结构
    expense_record = {
        'id': f"expense_{int(time.time())}",
        'amount': expense_info['amount'] or 0.0,
        'currency': expense_info['currency'],
        'category': expense_info['category'],
        'merchant': expense_info['merchant'],
        'description': expense_info['description'],
        'date': time.strftime('%Y-%m-%d'),
        'time': time.strftime('%H:%M:%S'),
        'source': 'voice_input'
    }
    
    return expense_record

def main():
    """主测试函数"""
    print("🎯 Voice Accounting - 支出语音识别测试")
    print(f"🤖 使用模型: gummy-chat-v1")
    print(f"🔑 API Key: {dashscope.api_key[:20]}...")
    
    # 测试支出场景识别
    test_expense_scenarios()
    
    # 测试真实语音识别
    test_real_voice_recognition()
    
    print("\n" + "="*50)
    print("📊 测试总结")
    print("="*50)
    print("✅ 支出信息提取算法测试完成")
    print("✅ 支持识别: 金额、商家、分类、描述")
    print("✅ 支持多种表达方式和货币单位")
    print("⚠️  实际语音识别需要音频文件测试")
    
    print("\n🚀 下一步:")
    print("1. 准备真实的支出语音样本进行测试")
    print("2. 优化信息提取算法的准确率")
    print("3. 集成到iOS应用的语音录制功能中")

if __name__ == "__main__":
    import time
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问语音识别测试运行器
一键运行所有测试，生成完整的测试报告
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_header(title):
    """打印测试标题"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)

def print_section(title):
    """打印章节标题"""
    print(f"\n📋 {title}")
    print("-"*40)

def run_test(script_name, description):
    """运行单个测试脚本"""
    print_section(f"运行测试: {description}")
    
    try:
        print(f"🚀 执行: python3 {script_name}")
        result = subprocess.run(
            [sys.executable, script_name], 
            capture_output=True, 
            text=True, 
            timeout=60
        )
        
        if result.returncode == 0:
            print("✅ 测试通过")
            print("📄 输出:")
            print(result.stdout)
            return True, result.stdout
        else:
            print("❌ 测试失败")
            print("📄 错误输出:")
            print(result.stderr)
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时 (60秒)")
        return False, "测试超时"
    except Exception as e:
        print(f"💥 测试异常: {e}")
        return False, str(e)

def check_dependencies():
    """检查依赖是否安装"""
    print_section("检查依赖")
    
    required_packages = ['dashscope', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip3 install -r requirements.txt")
        return False
    
    return True

def generate_report(test_results):
    """生成测试报告"""
    print_header("测试报告")
    
    # 统计结果
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['success'])
    failed_tests = total_tests - passed_tests
    
    # 基本统计
    print(f"📊 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests} ✅")
    print(f"   失败: {failed_tests} ❌")
    print(f"   成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    # 详细结果
    print(f"\n📋 详细结果:")
    for i, result in enumerate(test_results, 1):
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"   {i}. {result['name']}: {status}")
        if not result['success']:
            print(f"      错误: {result['output'][:100]}...")
    
    # 生成报告文件
    report_content = f"""# 通义千问语音识别API测试报告

## 测试时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试统计
- 总测试数: {total_tests}
- 通过: {passed_tests}
- 失败: {failed_tests}
- 成功率: {(passed_tests/total_tests)*100:.1f}%

## 详细结果
"""
    
    for i, result in enumerate(test_results, 1):
        status = "✅ 通过" if result['success'] else "❌ 失败"
        report_content += f"\n### {i}. {result['name']}: {status}\n"
        if result['success']:
            report_content += "```\n" + result['output'][:500] + "\n```\n"
        else:
            report_content += f"**错误信息:**\n```\n{result['output'][:500]}\n```\n"
    
    # 保存报告
    with open('test_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n💾 详细报告已保存到: test_report.md")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print_header("通义千问语音识别API测试套件")
    
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔑 API Key: sk-8fd0324578b24b558e520cb94696267a")
    print(f"🤖 模型: gummy-chat-v1")
    print(f"📁 测试目录: {os.getcwd()}")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装依赖包")
        return False
    
    # 定义测试列表
    tests = [
        {
            'script': 'test_qwen_voice_api.py',
            'name': '基础API功能测试',
            'description': '测试API连接、语音识别、翻译功能'
        },
        {
            'script': 'test_expense_voice_recognition.py', 
            'name': '支出场景识别测试',
            'description': '测试支出信息提取和分类功能'
        },
        {
            'script': 'voice_accounting_simulator.py',
            'name': 'Voice Accounting模拟器',
            'description': '完整的语音记账流程模拟'
        }
    ]
    
    # 运行测试
    test_results = []
    
    for test in tests:
        if os.path.exists(test['script']):
            success, output = run_test(test['script'], test['description'])
            test_results.append({
                'name': test['name'],
                'success': success,
                'output': output
            })
        else:
            print(f"⚠️  测试文件不存在: {test['script']}")
            test_results.append({
                'name': test['name'],
                'success': False,
                'output': f"测试文件不存在: {test['script']}"
            })
    
    # 生成报告
    all_passed = generate_report(test_results)
    
    # 最终结果
    if all_passed:
        print("\n🎉 所有测试通过！")
        print("✅ 通义千问API对接成功")
        print("✅ 语音识别功能正常")
        print("✅ 支出信息提取准确")
        print("\n🚀 可以开始集成到iOS应用中了！")
        return True
    else:
        print("\n⚠️  部分测试失败")
        print("请检查网络连接和API配置")
        print("详细错误信息请查看 test_report.md")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Voice Accounting Flask Backend
A RESTful API server for the Voice Accounting iOS app with database support
"""

from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from datetime import datetime, date
import os
import json
import pytz
from werkzeug.utils import secure_filename
import uuid
from config import config, SINGAPORE_TZ

# Initialize Flask app
app = Flask(__name__)

# Load configuration
config_name = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[config_name])

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)  # Enable CORS for iOS app

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 时区辅助函数
def get_singapore_now():
    """获取新加坡当前时间"""
    return datetime.now(SINGAPORE_TZ)

def utc_to_singapore(utc_dt):
    """将UTC时间转换为新加坡时间"""
    if utc_dt.tzinfo is None:
        utc_dt = pytz.utc.localize(utc_dt)
    return utc_dt.astimezone(SINGAPORE_TZ)

def singapore_to_utc(sg_dt):
    """将新加坡时间转换为UTC时间"""
    if sg_dt.tzinfo is None:
        sg_dt = SINGAPORE_TZ.localize(sg_dt)
    return sg_dt.astimezone(pytz.utc)

def format_singapore_time(dt):
    """格式化新加坡时间为ISO字符串"""
    if dt.tzinfo is None:
        # 假设数据库中的时间是UTC
        dt = pytz.utc.localize(dt)
    sg_time = dt.astimezone(SINGAPORE_TZ)
    return sg_time.isoformat()

# Database Models
class User(db.Model):
    """User model for multi-user support"""
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    currency = db.Column(db.String(3), default='USD')
    created_at = db.Column(db.DateTime, default=lambda: singapore_to_utc(get_singapore_now()).replace(tzinfo=None))
    
    # Relationships
    expenses = db.relationship('Expense', backref='user', lazy=True, cascade='all, delete-orphan')
    categories = db.relationship('Category', backref='user', lazy=True, cascade='all, delete-orphan')

class Category(db.Model):
    """Expense categories"""
    __tablename__ = 'categories'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    icon = db.Column(db.String(10), default='📝')
    color = db.Column(db.String(20), default='gray')
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: singapore_to_utc(get_singapore_now()).replace(tzinfo=None))
    
    # Relationships
    expenses = db.relationship('Expense', backref='category_obj', lazy=True)

class Expense(db.Model):
    """Expense records"""
    __tablename__ = 'expenses'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text, nullable=False)
    category_id = db.Column(db.String(36), db.ForeignKey('categories.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    date = db.Column(db.Date, default=date.today)
    time = db.Column(db.String(10), nullable=False)  # HH:MM format
    voice_file_path = db.Column(db.String(255), nullable=True)  # Path to voice recording
    created_at = db.Column(db.DateTime, default=lambda: singapore_to_utc(get_singapore_now()).replace(tzinfo=None))
    updated_at = db.Column(db.DateTime, default=lambda: singapore_to_utc(get_singapore_now()).replace(tzinfo=None), onupdate=lambda: singapore_to_utc(get_singapore_now()).replace(tzinfo=None))

# Helper Functions
def serialize_expense(expense):
    """Convert expense object to dictionary"""
    return {
        'id': expense.id,
        'amount': expense.amount,
        'description': expense.description,
        'category': expense.category_obj.name,
        'category_id': expense.category_id,
        'icon': expense.category_obj.icon,
        'color': expense.category_obj.color,
        'date': expense.date.isoformat(),
        'time': expense.time,
        'created_at': format_singapore_time(expense.created_at),
        'updated_at': format_singapore_time(expense.updated_at)
    }

def serialize_category(category):
    """Convert category object to dictionary"""
    return {
        'id': category.id,
        'name': category.name,
        'icon': category.icon,
        'color': category.color,
        'created_at': format_singapore_time(category.created_at)
    }

def serialize_user(user):
    """Convert user object to dictionary"""
    return {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'currency': user.currency,
        'created_at': format_singapore_time(user.created_at)
    }

# API Routes
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    singapore_time = get_singapore_now()
    return jsonify({
        'status': 'healthy',
        'message': 'Voice Accounting API is running',
        'timestamp': singapore_time.isoformat(),
        'timezone': 'Asia/Singapore (UTC+8)',
        'server_time': singapore_time.strftime('%Y-%m-%d %H:%M:%S %Z')
    })

@app.route('/api/users', methods=['POST'])
def create_user():
    """Create a new user"""
    data = request.get_json()
    
    if not data or 'username' not in data:
        return jsonify({'error': 'Username is required'}), 400
    
    # Check if user already exists
    existing_user = User.query.filter_by(username=data['username']).first()
    if existing_user:
        return jsonify({'error': 'Username already exists'}), 409
    
    # Create new user
    user = User(
        username=data['username'],
        email=data.get('email'),
        currency=data.get('currency', 'USD')
    )
    
    try:
        db.session.add(user)
        db.session.commit()
        
        # Create default categories for new user
        create_default_categories(user.id)
        
        return jsonify({
            'message': 'User created successfully',
            'user': serialize_user(user)
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<user_id>', methods=['GET'])
def get_user(user_id):
    """Get user information"""
    user = User.query.get_or_404(user_id)
    return jsonify({'user': serialize_user(user)})

@app.route('/api/users/<user_id>', methods=['PUT'])
def update_user(user_id):
    """Update user information"""
    user = User.query.get_or_404(user_id)
    data = request.get_json()
    
    if 'currency' in data:
        user.currency = data['currency']
    if 'email' in data:
        user.email = data['email']
    
    try:
        db.session.commit()
        return jsonify({
            'message': 'User updated successfully',
            'user': serialize_user(user)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def create_default_categories(user_id):
    """Create default expense categories for a new user"""
    default_categories = [
        {'name': 'Food & Dining', 'icon': '🍽️', 'color': 'orange'},
        {'name': 'Transportation', 'icon': '🚗', 'color': 'blue'},
        {'name': 'Shopping', 'icon': '🛍️', 'color': 'green'},
        {'name': 'Entertainment', 'icon': '🎬', 'color': 'purple'},
        {'name': 'Healthcare', 'icon': '🏥', 'color': 'red'},
        {'name': 'Bills & Utilities', 'icon': '📄', 'color': 'yellow'},
        {'name': 'Education', 'icon': '📚', 'color': 'indigo'},
        {'name': 'Others', 'icon': '📝', 'color': 'gray'}
    ]
    
    for cat_data in default_categories:
        category = Category(
            name=cat_data['name'],
            icon=cat_data['icon'],
            color=cat_data['color'],
            user_id=user_id
        )
        db.session.add(category)
    
    db.session.commit()

# Category Routes
@app.route('/api/users/<user_id>/categories', methods=['GET'])
def get_categories(user_id):
    """Get all categories for a user"""
    user = User.query.get_or_404(user_id)
    categories = Category.query.filter_by(user_id=user_id).all()
    return jsonify({
        'categories': [serialize_category(cat) for cat in categories]
    })

@app.route('/api/users/<user_id>/categories', methods=['POST'])
def create_category(user_id):
    """Create a new category"""
    user = User.query.get_or_404(user_id)
    data = request.get_json()

    if not data or 'name' not in data:
        return jsonify({'error': 'Category name is required'}), 400

    category = Category(
        name=data['name'],
        icon=data.get('icon', '📝'),
        color=data.get('color', 'gray'),
        user_id=user_id
    )

    try:
        db.session.add(category)
        db.session.commit()
        return jsonify({
            'message': 'Category created successfully',
            'category': serialize_category(category)
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Expense Routes
@app.route('/api/users/<user_id>/expenses', methods=['GET'])
def get_expenses(user_id):
    """Get all expenses for a user"""
    user = User.query.get_or_404(user_id)

    # Optional query parameters
    limit = request.args.get('limit', type=int)
    offset = request.args.get('offset', 0, type=int)
    category_id = request.args.get('category_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Build query
    query = Expense.query.filter_by(user_id=user_id)

    if category_id:
        query = query.filter_by(category_id=category_id)

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Expense.date >= start_date_obj)
        except ValueError:
            return jsonify({'error': 'Invalid start_date format. Use YYYY-MM-DD'}), 400

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Expense.date <= end_date_obj)
        except ValueError:
            return jsonify({'error': 'Invalid end_date format. Use YYYY-MM-DD'}), 400

    # Order by date and time (newest first)
    query = query.order_by(Expense.date.desc(), Expense.time.desc())

    # Apply pagination
    if limit:
        query = query.offset(offset).limit(limit)

    expenses = query.all()

    return jsonify({
        'expenses': [serialize_expense(exp) for exp in expenses],
        'total': Expense.query.filter_by(user_id=user_id).count()
    })

@app.route('/api/users/<user_id>/expenses', methods=['POST'])
def create_expense(user_id):
    """Create a new expense"""
    user = User.query.get_or_404(user_id)
    data = request.get_json()

    # Validate required fields
    required_fields = ['amount', 'description', 'category_id', 'time']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'{field} is required'}), 400

    # Validate category belongs to user
    category = Category.query.filter_by(id=data['category_id'], user_id=user_id).first()
    if not category:
        return jsonify({'error': 'Invalid category'}), 400

    # Parse date if provided, otherwise use today
    expense_date = date.today()
    if 'date' in data:
        try:
            expense_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400

    expense = Expense(
        amount=float(data['amount']),
        description=data['description'],
        category_id=data['category_id'],
        user_id=user_id,
        date=expense_date,
        time=data['time'],
        voice_file_path=data.get('voice_file_path')
    )

    try:
        db.session.add(expense)
        db.session.commit()
        return jsonify({
            'message': 'Expense created successfully',
            'expense': serialize_expense(expense)
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<user_id>/expenses/<expense_id>', methods=['PUT'])
def update_expense(user_id, expense_id):
    """Update an expense"""
    user = User.query.get_or_404(user_id)
    expense = Expense.query.filter_by(id=expense_id, user_id=user_id).first_or_404()
    data = request.get_json()

    # Update fields if provided
    if 'amount' in data:
        expense.amount = float(data['amount'])
    if 'description' in data:
        expense.description = data['description']
    if 'category_id' in data:
        # Validate category belongs to user
        category = Category.query.filter_by(id=data['category_id'], user_id=user_id).first()
        if not category:
            return jsonify({'error': 'Invalid category'}), 400
        expense.category_id = data['category_id']
    if 'date' in data:
        try:
            expense.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
    if 'time' in data:
        expense.time = data['time']

    expense.updated_at = singapore_to_utc(get_singapore_now()).replace(tzinfo=None)

    try:
        db.session.commit()
        return jsonify({
            'message': 'Expense updated successfully',
            'expense': serialize_expense(expense)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<user_id>/expenses/<expense_id>', methods=['DELETE'])
def delete_expense(user_id, expense_id):
    """Delete an expense"""
    user = User.query.get_or_404(user_id)
    expense = Expense.query.filter_by(id=expense_id, user_id=user_id).first_or_404()

    try:
        db.session.delete(expense)
        db.session.commit()
        return jsonify({'message': 'Expense deleted successfully'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Create database tables
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")

    # Run the app
    app.run(debug=True, host='0.0.0.0', port=5000)

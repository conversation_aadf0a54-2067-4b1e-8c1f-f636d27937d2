#!/usr/bin/env python3
"""
AWS RDS MySQL设置脚本
用于配置Voice Accounting应用的AWS RDS MySQL数据库
"""

import os
import sys
import pymysql
from sqlalchemy import create_engine, text
from config import config
from main import app, db

# AWS RDS配置信息
RDS_CONFIG = {
    'host': 'db-talktab.cluwoksc8z3h.ap-southeast-1.rds.amazonaws.com',
    'port': 3306,
    'master_user': 'admin',
    'master_password': 'Flzx3000c!',
    'region': 'ap-southeast-1'
}

def test_rds_connection():
    """测试AWS RDS连接"""
    try:
        connection = pymysql.connect(
            host=RDS_CONFIG['host'],
            port=RDS_CONFIG['port'],
            user=RDS_CONFIG['master_user'],
            password=RDS_CONFIG['master_password'],
            charset='utf8mb4',
            connect_timeout=30
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ AWS RDS连接成功!")
            print(f"📍 Endpoint: {RDS_CONFIG['host']}")
            print(f"🔢 MySQL版本: {version[0]}")
            
            # 检查时区
            cursor.execute("SELECT @@time_zone, NOW()")
            timezone_info = cursor.fetchone()
            print(f"🕐 时区: {timezone_info[0]}, 当前时间: {timezone_info[1]}")
        
        connection.close()
        return True
    except Exception as e:
        print(f"❌ AWS RDS连接失败: {e}")
        return False

def setup_database_and_user():
    """在RDS上创建应用数据库和用户"""
    
    # 应用数据库配置
    db_name = 'voice_accounting'
    app_user = 'voice_user'
    app_password = 'VoiceApp2024!@#'  # 强密码
    
    try:
        connection = pymysql.connect(
            host=RDS_CONFIG['host'],
            port=RDS_CONFIG['port'],
            user=RDS_CONFIG['master_user'],
            password=RDS_CONFIG['master_password'],
            charset='utf8mb4',
            connect_timeout=30
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 '{db_name}' 创建成功")
            
            # 创建应用用户
            cursor.execute(f"CREATE USER IF NOT EXISTS '{app_user}'@'%' IDENTIFIED WITH mysql_native_password BY '{app_password}'")
            print(f"✅ 用户 '{app_user}' 创建成功")
            
            # 授予权限
            cursor.execute(f"GRANT ALL PRIVILEGES ON {db_name}.* TO '{app_user}'@'%'")
            cursor.execute("FLUSH PRIVILEGES")
            print(f"✅ 权限授予成功")
            
            # 验证用户创建
            cursor.execute("SELECT User, Host FROM mysql.user WHERE User = %s", (app_user,))
            users = cursor.fetchall()
            print(f"📋 创建的用户: {users}")
        
        connection.commit()
        connection.close()
        
        return {
            'host': RDS_CONFIG['host'],
            'port': RDS_CONFIG['port'],
            'database': db_name,
            'username': app_user,
            'password': app_password
        }
        
    except Exception as e:
        print(f"❌ 创建数据库和用户失败: {e}")
        return None

def test_app_user_connection(db_config):
    """测试应用用户连接"""
    try:
        connection = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['username'],
            password=db_config['password'],
            database=db_config['database'],
            charset='utf8mb4',
            connect_timeout=30
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT DATABASE(), USER()")
            result = cursor.fetchone()
            print(f"✅ 应用用户连接成功!")
            print(f"📋 当前数据库: {result[0]}")
            print(f"👤 当前用户: {result[1]}")
        
        connection.close()
        return True
    except Exception as e:
        print(f"❌ 应用用户连接失败: {e}")
        return False

def create_env_file(db_config):
    """创建.env配置文件"""
    database_url = f"mysql+pymysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset=utf8mb4"
    
    env_content = f"""# Voice Accounting Backend Environment Variables
# AWS RDS MySQL Configuration

# Flask Configuration
FLASK_ENV=production
SECRET_KEY=voice-accounting-aws-rds-{os.urandom(16).hex()}

# AWS RDS Database Configuration
DATABASE_URL={database_url}

# Database Settings
DB_HOST={db_config['host']}
DB_PORT={db_config['port']}
DB_NAME={db_config['database']}
DB_USER={db_config['username']}
DB_PASSWORD={db_config['password']}

# Connection Pool Settings (AWS RDS优化)
DB_POOL_SIZE=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Flask Settings
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=********

# AWS Region
AWS_REGION=ap-southeast-1

# CORS Settings (如果需要跨域访问)
CORS_ORIGINS=*
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ .env文件创建成功!")
    print(f"🔗 数据库连接字符串: {database_url.replace(db_config['password'], '***')}")

def setup_tables(db_config):
    """创建数据库表结构"""
    try:
        database_url = f"mysql+pymysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset=utf8mb4"
        
        # 设置环境变量
        os.environ['DATABASE_URL'] = database_url
        app.config['SQLALCHEMY_DATABASE_URI'] = database_url
        
        with app.app_context():
            # 创建所有表
            db.create_all()
            print("✅ 数据库表创建成功!")
            
            # 显示创建的表
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📋 创建的表: {', '.join(tables)}")
            
        return True
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def create_sample_data(db_config):
    """创建示例数据"""
    try:
        database_url = f"mysql+pymysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}?charset=utf8mb4"
        
        os.environ['DATABASE_URL'] = database_url
        app.config['SQLALCHEMY_DATABASE_URI'] = database_url
        
        with app.app_context():
            from main import User, Category, Expense
            from datetime import date, datetime
            
            # 检查是否已有示例用户
            sample_user = User.query.filter_by(username='demo_user').first()
            if sample_user:
                print("📝 示例用户已存在，跳过创建")
                return True
            
            # 创建示例用户
            user = User(
                username='demo_user',
                email='<EMAIL>',
                currency='USD'
            )
            db.session.add(user)
            db.session.flush()
            
            # 创建分类
            categories_data = [
                {'name': 'Food & Dining', 'icon': '🍽️', 'color': 'orange'},
                {'name': 'Transportation', 'icon': '🚗', 'color': 'blue'},
                {'name': 'Shopping', 'icon': '🛍️', 'color': 'green'},
                {'name': 'Entertainment', 'icon': '🎬', 'color': 'purple'},
                {'name': 'Healthcare', 'icon': '🏥', 'color': 'red'},
                {'name': 'Bills & Utilities', 'icon': '📄', 'color': 'yellow'},
                {'name': 'Education', 'icon': '📚', 'color': 'indigo'},
                {'name': 'Others', 'icon': '📝', 'color': 'gray'}
            ]
            
            categories = []
            for cat_data in categories_data:
                category = Category(
                    name=cat_data['name'],
                    icon=cat_data['icon'],
                    color=cat_data['color'],
                    user_id=user.id
                )
                db.session.add(category)
                categories.append(category)
            
            db.session.flush()
            
            # 创建示例支出记录
            sample_expenses = [
                {
                    'amount': 25.50,
                    'description': 'Lunch at restaurant',
                    'category': categories[0],
                    'time': '12:30',
                    'date': date.today()
                },
                {
                    'amount': 8.00,
                    'description': 'Morning coffee',
                    'category': categories[0],
                    'time': '08:30',
                    'date': date.today()
                },
                {
                    'amount': 15.00,
                    'description': 'Bus fare',
                    'category': categories[1],
                    'time': '09:00',
                    'date': date.today()
                }
            ]
            
            for exp_data in sample_expenses:
                expense = Expense(
                    amount=exp_data['amount'],
                    description=exp_data['description'],
                    category_id=exp_data['category'].id,
                    user_id=user.id,
                    time=exp_data['time'],
                    date=exp_data['date']
                )
                db.session.add(expense)
            
            db.session.commit()
            print("✅ 示例数据创建成功!")
            print(f"👤 示例用户ID: {user.id}")
            print(f"📊 创建了 {len(categories)} 个分类和 {len(sample_expenses)} 条支出记录")
            
        return True
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        db.session.rollback()
        return False

def main():
    """主设置函数"""
    print("🚀 Voice Accounting AWS RDS MySQL设置")
    print("=" * 50)
    print(f"🌐 RDS Endpoint: {RDS_CONFIG['host']}")
    print(f"📍 Region: {RDS_CONFIG['region']}")
    
    # 步骤1: 测试RDS连接
    print("\n1️⃣ 测试AWS RDS连接...")
    if not test_rds_connection():
        print("💡 请检查:")
        print("   - RDS实例是否运行中")
        print("   - 安全组是否允许3306端口")
        print("   - 网络连接是否正常")
        sys.exit(1)
    
    # 步骤2: 创建数据库和用户
    print("\n2️⃣ 创建应用数据库和用户...")
    db_config = setup_database_and_user()
    if not db_config:
        sys.exit(1)
    
    # 步骤3: 测试应用用户连接
    print("\n3️⃣ 测试应用用户连接...")
    if not test_app_user_connection(db_config):
        sys.exit(1)
    
    # 步骤4: 创建环境配置文件
    print("\n4️⃣ 创建环境配置文件...")
    create_env_file(db_config)
    
    # 步骤5: 创建表结构
    print("\n5️⃣ 创建数据库表...")
    if not setup_tables(db_config):
        sys.exit(1)
    
    # 步骤6: 创建示例数据
    print("\n6️⃣ 创建示例数据...")
    if not create_sample_data(db_config):
        sys.exit(1)
    
    print("\n🎉 AWS RDS MySQL设置完成!")
    print("\n📋 配置信息:")
    print(f"   🌐 RDS Endpoint: {db_config['host']}")
    print(f"   📊 数据库: {db_config['database']}")
    print(f"   👤 用户: {db_config['username']}")
    print(f"   🔐 密码: {db_config['password']}")
    
    print("\n📋 下一步:")
    print("1. 启动Flask服务器: python run.py")
    print("2. 测试API: curl http://localhost:5000/api/health")
    print("3. 在iOS应用中切换到NetworkDataManager")
    print("4. 测试数据同步功能")
    
    print("\n💡 重要提示:")
    print("- RDS实例会产生费用，请注意AWS账单")
    print("- 建议设置RDS自动备份")
    print("- 生产环境请修改默认密码")

if __name__ == '__main__':
    main()

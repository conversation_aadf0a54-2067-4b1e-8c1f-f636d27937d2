#!/usr/bin/env python3
"""
Test script for number parsing functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.voice_processor import ExpenseInfoExtractor

def test_number_parsing():
    extractor = ExpenseInfoExtractor()
    
    test_cases = [
        # Test case 1: Comma-separated numbers
        ("I spent 1,1, 10 to buy a bike", 1110.0),
        ("I spend 1,1, 10 to buy a bike", 1110.0),
        
        # Test case 2: Spoken numbers
        ("I spent one one ten dollars", 1110.0),
        ("I paid one one ten", 1110.0),
        
        # Test case 3: Regular numbers (should still work)
        ("I spent 50 dollars", 50.0),
        ("I paid 25.50", 25.50),
        
        # Test case 4: Mixed cases
        ("I spent 1, 2, 3 dollars", 123.0),
        ("Cost me 5,0,0", 500.0),
    ]
    
    print("Testing number parsing functionality:")
    print("=" * 50)
    
    for text, expected in test_cases:
        amount, currency = extractor.extract_amount_and_currency(text)
        status = "✅ PASS" if amount == expected else "❌ FAIL"
        print(f"{status} '{text}' -> {amount} (expected: {expected})")
    
    print("\nTesting full expense extraction:")
    print("=" * 50)
    
    # Test full expense extraction
    test_text = "I spend 1,1, 10 to buy a bike"
    result = extractor.extract_expense_info(test_text)
    print(f"Input: {test_text}")
    print(f"Result: {result}")

if __name__ == "__main__":
    test_number_parsing()

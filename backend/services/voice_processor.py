"""
Voice Processing Service using Qwen Omni AI
Handles voice recognition and expense information extraction
"""

import os
import re
import ssl
import time
import logging
from typing import Dict, Op<PERSON>, Tuple, List
from datetime import datetime

# SSL fix for development
try:
    import certifi
    os.environ['SSL_CERT_FILE'] = certifi.where()
    os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
except ImportError:
    pass

ssl._create_default_https_context = ssl._create_unverified_context

try:
    import dashscope
    from dashscope.audio.asr import *
except ImportError as e:
    logging.error(f"DashScope SDK not installed: {e}")
    dashscope = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceProcessingCallback(TranslationRecognizerCallback):
    """Callback handler for voice recognition"""

    def __init__(self):
        self.final_text = ""
        self.is_complete = False
        self.error_message = None
        self.confidence = 0.0

    def on_open(self) -> None:
        logger.info("Voice recognition started")

    def on_close(self) -> None:
        logger.info("Voice recognition ended")

    def on_event(self, request_id, transcription_result, translation_result, usage):
        if transcription_result is not None:
            logger.info(f"Recognition result: {transcription_result.text}")

            # Save final result when sentence is complete
            if transcription_result.is_sentence_end:
                self.final_text = transcription_result.text
                self.confidence = 0.95  # Mock confidence score
                logger.info(f"Final recognition: {self.final_text}")

    def on_error(self, message) -> None:
        logger.error(f"Recognition error: {message}")
        self.error_message = str(message)

    def on_complete(self) -> None:
        logger.info("Voice recognition completed")
        self.is_complete = True

class ExpenseInfoExtractor:
    """Extract expense information from recognized text"""
    
    def __init__(self):
        # Singapore localized configuration
        self.default_currency = 'SGD'
        self.currency_patterns = {
            'SGD': ['dollar', 'dollars', '块', '元', 'sgd', '新币'],
            'USD': ['usd', 'us dollar', '美元'],
            'MYR': ['myr', 'ringgit', '马币'],
            'CNY': ['rmb', '人民币', '块钱'],
            'THB': ['thb', 'baht', '泰铢'],
        }
        
        # Singapore merchants and categories
        self.merchant_categories = {
            'Food & Dining': [
                'hawker center', 'hawker', 'food court', 'kopitiam',
                'mcdonald', 'kfc', 'burger king', 'subway', 'pizza hut',
                'starbucks', 'coffee bean', 'ya kun', 'toast box',
                'din tai fung', 'crystal jade', 'jumbo seafood',
                '麦当劳', '肯德基', '星巴克', '亚坤', '土司工坊',
                'restaurant', 'cafe', 'lunch', 'dinner', 'breakfast',
                '午餐', '晚餐', '早餐', '吃饭', '喝咖啡'
            ],
            'Transportation': [
                'grab', 'gojek', 'taxi', 'uber', 'mrt', 'bus',
                'ez-link', 'comfort', 'citycab', 'trans-cab',
                '地铁', '公交', '打车', '出租车', '巴士',
                'transport', 'ride', 'trip'
            ],
            'Shopping': [
                'ntuc', 'cold storage', 'giant', 'sheng siong',
                'mustafa', 'uniqlo', 'h&m', 'zara', 'courts',
                'ion orchard', 'vivocity', 'marina bay sands',
                '超市', '商场', '购物中心', '买', '购买',
                'shopping', 'mall', 'store', 'supermarket'
            ],
            'Entertainment': [
                'cinema', 'ktv', 'golden village', 'cathay',
                'sentosa', 'universal studios', 'zoo', 'aquarium',
                '电影院', 'KTV', '圣淘沙', '动物园',
                'movie', 'film', 'entertainment', 'game'
            ],
            'Health & Fitness': [
                'gym', 'fitness', 'hospital', 'clinic', 'pharmacy',
                '健身房', '医院', '药店', '诊所',
                'doctor', 'medicine', 'health'
            ]
        }
    
    def extract_amount_and_currency(self, text: str) -> Tuple[Optional[float], str]:
        """Extract amount and currency from text"""

        # First, try to parse spoken number sequences like "one one ten" -> "1110"
        amount = self._parse_spoken_numbers(text)

        if amount is None:
            # Fallback to regex patterns for standard formats
            text_lower = text.lower()

            # First, try to match comma-separated patterns specifically
            comma_patterns = [
                r'(?:spent|spend|paid|cost|花了)\s*(\d+(?:,\s*\d+)+)',  # "spent 1,1, 10" or "spend 1,1, 10"
                r'(?:cost\s+me)\s*(\d+(?:,\s*\d+)+)',  # "cost me 5,0,0"
                r'(\d+(?:,\s*\d+)+)\s*(?:dollar|dollars|块|元|sgd|新币)',  # "1,1, 10 dollars"
            ]

            for pattern in comma_patterns:
                match = re.search(pattern, text_lower)
                if match:
                    amount_str = match.group(1)
                    amount = self._parse_comma_separated_numbers(amount_str)
                    if amount is not None:
                        break

            # If no comma pattern matched, try regular patterns
            if amount is None:
                amount_patterns = [
                    r'(\d+(?:\.\d{1,2})?)\s*(?:dollar|dollars|块|元|sgd|新币)',
                    r'花了\s*(\d+(?:\.\d{1,2})?)',
                    r'(\d+(?:\.\d{1,2})?)\s*钱',
                    r'cost\s*(\d+(?:\.\d{1,2})?)',
                    r'paid\s*(\d+(?:\.\d{1,2})?)',
                    r'spent\s*(\d+(?:\.\d{1,2})?)',
                    r'(\d+(?:\.\d{1,2})?)\s*(?:SGD|USD|MYR|CNY|THB)',
                    r'charge[d]?\s*(\d+(?:\.\d{1,2})?)',
                ]

                # Extract amount
                for pattern in amount_patterns:
                    match = re.search(pattern, text_lower)
                    if match:
                        amount = float(match.group(1))
                        break

        currency = self.default_currency
        text_lower = text.lower()

        # Extract amount (this part remains the same for now)
        
        # Detect currency
        for curr, keywords in self.currency_patterns.items():
            for keyword in keywords:
                if keyword in text_lower:
                    currency = curr
                    break
        
        return amount, currency
    
    def extract_merchant_and_category(self, text: str) -> Tuple[str, str]:
        """Extract merchant and category from text"""
        text_lower = text.lower()
        
        for category, merchants in self.merchant_categories.items():
            for merchant in merchants:
                if merchant in text_lower:
                    # Extract the actual merchant name from text
                    merchant_name = self._extract_merchant_name(text, merchant)
                    return merchant_name, category
        
        return '', 'Other'
    
    def _extract_merchant_name(self, text: str, keyword: str) -> str:
        """Extract actual merchant name from text"""
        # Common merchant name mappings
        merchant_mapping = {
            'mcdonald': "McDonald's",
            'kfc': 'KFC',
            'starbucks': 'Starbucks',
            'grab': 'Grab',
            'ntuc': 'NTUC',
            'ya kun': 'Ya Kun',
            'golden village': 'Golden Village',
            '麦当劳': "McDonald's",
            '星巴克': 'Starbucks',
            '亚坤': 'Ya Kun'
        }
        
        return merchant_mapping.get(keyword.lower(), keyword.title())

    def _parse_spoken_numbers(self, text: str) -> Optional[float]:
        """Parse spoken number sequences like 'one one ten' -> 1110"""
        # Word to digit mapping
        word_to_digit = {
            'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
            'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9',
            'ten': '10', 'eleven': '11', 'twelve': '12', 'thirteen': '13',
            'fourteen': '14', 'fifteen': '15', 'sixteen': '16', 'seventeen': '17',
            'eighteen': '18', 'nineteen': '19', 'twenty': '20', 'thirty': '30',
            'forty': '40', 'fifty': '50', 'sixty': '60', 'seventy': '70',
            'eighty': '80', 'ninety': '90', 'hundred': '100', 'thousand': '1000'
        }

        text_lower = text.lower()

        # Look for patterns like "one one ten" or "eleven ten"
        # Pattern 1: Individual digits spoken as words (one one ten = 1110)
        pattern1 = r'\b((?:one|two|three|four|five|six|seven|eight|nine|zero)(?:\s+(?:one|two|three|four|five|six|seven|eight|nine|zero|ten|eleven|twelve|thirteen|fourteen|fifteen|sixteen|seventeen|eighteen|nineteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety))*)\b'

        match = re.search(pattern1, text_lower)
        if match:
            words = match.group(1).split()
            # Convert each word to digit and concatenate
            digits = []
            for word in words:
                if word in word_to_digit:
                    digit_str = word_to_digit[word]
                    # For numbers like "ten", "eleven", etc., treat as individual digits if part of sequence
                    if len(words) > 1 and len(digit_str) > 1:
                        # If it's a multi-digit number in a sequence, break it down
                        digits.extend(list(digit_str))
                    else:
                        digits.append(digit_str)

            if digits:
                try:
                    # Join all digits to form the final number
                    number_str = ''.join(digits)
                    return float(number_str)
                except ValueError:
                    pass

        return None

    def _parse_comma_separated_numbers(self, text: str) -> Optional[float]:
        """Parse comma-separated numbers like '1,1, 10' -> 1110"""
        try:
            # Remove spaces and split by comma
            parts = [part.strip() for part in text.split(',') if part.strip()]
            # Concatenate all parts
            number_str = ''.join(parts)
            return float(number_str)
        except (ValueError, AttributeError):
            return None

    def extract_expense_info(self, recognized_text: str, confidence: float = 0.95) -> Dict:
        """Extract complete expense information from recognized text"""
        
        # Extract amount and currency
        amount, currency = self.extract_amount_and_currency(recognized_text)
        
        # Extract merchant and category
        merchant, category = self.extract_merchant_and_category(recognized_text)
        
        # Generate expense record
        expense_info = {
            'amount': amount or 0.0,
            'currency': currency,
            'category': category,
            'merchant': merchant,
            'description': recognized_text,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'time': datetime.now().strftime('%H:%M:%S'),
            'confidence': confidence,
            'raw_text': recognized_text
        }
        
        logger.info(f"Extracted expense info: {expense_info}")
        return expense_info

class VoiceProcessor:
    """Main voice processing service"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or "sk-8fd0324578b24b558e520cb94696267a"
        self.extractor = ExpenseInfoExtractor()
        
        if dashscope:
            dashscope.api_key = self.api_key
            logger.info("DashScope SDK initialized")
        else:
            logger.warning("DashScope SDK not available")
    
    def process_audio_file(self, audio_file_path: str) -> Dict:
        """Process audio file and extract expense information"""
        
        if not dashscope:
            # Fallback: mock processing for development
            return self._mock_processing(audio_file_path)
        
        try:
            # Create callback handler
            callback = VoiceProcessingCallback()

            # Create recognizer
            recognizer = TranslationRecognizerChat(
                model="gummy-chat-v1",
                format="wav",  # Assume WAV format for now
                sample_rate=16000,
                transcription_enabled=True,
                translation_enabled=False,
                callback=callback,
            )

            # Start recognition
            recognizer.start()
            logger.info(f"Processing audio file: {audio_file_path}")

            # Read and send audio data
            with open(audio_file_path, 'rb') as f:
                chunk_size = 12800  # 12.8KB chunks
                while True:
                    audio_data = f.read(chunk_size)
                    if not audio_data:
                        break

                    if not recognizer.send_audio_frame(audio_data):
                        logger.info("Sentence end detected")
                        break

                    time.sleep(0.1)  # Simulate real-time streaming

            recognizer.stop()

            # Wait for completion
            timeout = 30
            start_time = time.time()
            while not callback.is_complete and not callback.error_message:
                if time.time() - start_time > timeout:
                    logger.error("Recognition timeout")
                    break
                time.sleep(0.1)

            if callback.error_message:
                logger.error(f"Recognition failed: {callback.error_message}")
                return {'error': callback.error_message}

            if not callback.final_text:
                logger.warning("No recognition result")
                return {'error': 'No speech detected'}

            # Check if the text contains multiple expenses
            expense_list = self._parse_multiple_expenses(callback.final_text)

            if len(expense_list) > 1:
                # Multiple expenses detected
                logger.info(f"Multiple expenses detected: {len(expense_list)} items")
                return {
                    'status': 'success',
                    'recognized_text': callback.final_text,
                    'multiple_expenses': expense_list,
                    'expense_count': len(expense_list)
                }
            else:
                # Single expense
                expense_info = self.extractor.extract_expense_info(callback.final_text, callback.confidence)
                return {
                    'status': 'success',
                    'recognized_text': callback.final_text,
                    'expense_info': expense_info
                }
            
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
            return {'error': str(e)}
    
    def _parse_multiple_expenses(self, text: str) -> List[Dict]:
        """Parse text that may contain multiple expense statements"""
        # Common separators for multiple expenses
        separators = [
            '，然后', '，接着', '，之后', '，还有', '，另外',
            ', then', ', also', ', and', '; ', '. ', '。'
        ]

        # Try to split the text by separators
        segments = [text]
        for separator in separators:
            new_segments = []
            for segment in segments:
                if separator in segment:
                    new_segments.extend(segment.split(separator))
                else:
                    new_segments.append(segment)
            segments = new_segments

        # Clean up segments
        segments = [s.strip() for s in segments if s.strip()]

        # Process each segment as a potential expense
        expense_list = []
        for segment in segments:
            # Check if this segment contains expense information
            expense_info = self.extractor.extract_expense_info(segment, 0.95)
            if expense_info['amount'] > 0:  # Only include if amount is valid
                expense_list.append(expense_info)

        # If no valid expenses found, return the original text as a single expense
        if not expense_list and text.strip():
            expense_list = [self.extractor.extract_expense_info(text, 0.95)]

        return expense_list

    def _mock_processing(self, audio_file_path: str) -> Dict:
        """Mock processing for development when DashScope is not available"""
        logger.info(f"Mock processing audio file: {audio_file_path}")

        # Simulate processing delay
        time.sleep(1)

        # Mock recognition result with multiple expenses
        mock_text = "I spent 15 dollars at McDonald's for lunch, then I paid 20 dollars for gas"
        expense_list = self._parse_multiple_expenses(mock_text)

        if len(expense_list) > 1:
            return {
                'status': 'success',
                'recognized_text': mock_text,
                'multiple_expenses': expense_list,
                'expense_count': len(expense_list)
            }
        else:
            return {
                'status': 'success',
                'recognized_text': mock_text,
                'expense_info': expense_list[0]
            }

# Global processor instance
voice_processor = VoiceProcessor()

"""
Voice Processing Service using Qwen Omni AI
Handles voice recognition and expense information extraction
"""

import os
import re
import ssl
import time
import logging
from typing import Dict, Optional, Tuple
from datetime import datetime

# SSL fix for development
try:
    import certifi
    os.environ['SSL_CERT_FILE'] = certifi.where()
    os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
except ImportError:
    pass

ssl._create_default_https_context = ssl._create_unverified_context

try:
    import dashscope
    from dashscope.audio.asr import *
except ImportError as e:
    logging.error(f"DashScope SDK not installed: {e}")
    dashscope = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceProcessingCallback(TranslationRecognizerCallback):
    """Callback handler for voice recognition"""
    
    def __init__(self):
        self.final_text = ""
        self.is_complete = False
        self.error_message = None
        self.confidence = 0.0
        
    def on_open(self) -> None:
        logger.info("Voice recognition started")
        
    def on_close(self) -> None:
        logger.info("Voice recognition ended")
        
    def on_event(self, request_id, transcription_result, translation_result, usage):
        if transcription_result is not None:
            logger.info(f"Recognition result: {transcription_result.text}")
            
            # Save final result when sentence is complete
            if transcription_result.is_sentence_end:
                self.final_text = transcription_result.text
                self.confidence = 0.95  # Mock confidence score
                logger.info(f"Final recognition: {self.final_text}")
                
    def on_error(self, message) -> None:
        logger.error(f"Recognition error: {message}")
        self.error_message = str(message)
        
    def on_complete(self) -> None:
        logger.info("Voice recognition completed")
        self.is_complete = True

class ExpenseInfoExtractor:
    """Extract expense information from recognized text"""
    
    def __init__(self):
        # Singapore localized configuration
        self.default_currency = 'SGD'
        self.currency_patterns = {
            'SGD': ['dollar', 'dollars', '块', '元', 'sgd', '新币'],
            'USD': ['usd', 'us dollar', '美元'],
            'MYR': ['myr', 'ringgit', '马币'],
            'CNY': ['rmb', '人民币', '块钱'],
            'THB': ['thb', 'baht', '泰铢'],
        }
        
        # Singapore merchants and categories
        self.merchant_categories = {
            'Food & Dining': [
                'hawker center', 'hawker', 'food court', 'kopitiam',
                'mcdonald', 'kfc', 'burger king', 'subway', 'pizza hut',
                'starbucks', 'coffee bean', 'ya kun', 'toast box',
                'din tai fung', 'crystal jade', 'jumbo seafood',
                '麦当劳', '肯德基', '星巴克', '亚坤', '土司工坊',
                'restaurant', 'cafe', 'lunch', 'dinner', 'breakfast',
                '午餐', '晚餐', '早餐', '吃饭', '喝咖啡'
            ],
            'Transportation': [
                'grab', 'gojek', 'taxi', 'uber', 'mrt', 'bus',
                'ez-link', 'comfort', 'citycab', 'trans-cab',
                '地铁', '公交', '打车', '出租车', '巴士',
                'transport', 'ride', 'trip'
            ],
            'Shopping': [
                'ntuc', 'cold storage', 'giant', 'sheng siong',
                'mustafa', 'uniqlo', 'h&m', 'zara', 'courts',
                'ion orchard', 'vivocity', 'marina bay sands',
                '超市', '商场', '购物中心', '买', '购买',
                'shopping', 'mall', 'store', 'supermarket'
            ],
            'Entertainment': [
                'cinema', 'ktv', 'golden village', 'cathay',
                'sentosa', 'universal studios', 'zoo', 'aquarium',
                '电影院', 'KTV', '圣淘沙', '动物园',
                'movie', 'film', 'entertainment', 'game'
            ],
            'Health & Fitness': [
                'gym', 'fitness', 'hospital', 'clinic', 'pharmacy',
                '健身房', '医院', '药店', '诊所',
                'doctor', 'medicine', 'health'
            ]
        }
    
    def extract_amount_and_currency(self, text: str) -> Tuple[Optional[float], str]:
        """Extract amount and currency from text"""
        # Amount extraction patterns
        amount_patterns = [
            r'(\d+(?:\.\d{1,2})?)\s*(?:dollar|dollars|块|元|sgd|新币)',
            r'花了\s*(\d+(?:\.\d{1,2})?)',
            r'(\d+(?:\.\d{1,2})?)\s*钱',
            r'cost\s*(\d+(?:\.\d{1,2})?)',
            r'paid\s*(\d+(?:\.\d{1,2})?)',
            r'spent\s*(\d+(?:\.\d{1,2})?)',
            r'(\d+(?:\.\d{1,2})?)\s*(?:SGD|USD|MYR|CNY|THB)',
            r'charge[d]?\s*(\d+(?:\.\d{1,2})?)',
        ]
        
        amount = None
        currency = self.default_currency
        text_lower = text.lower()
        
        # Extract amount
        for pattern in amount_patterns:
            match = re.search(pattern, text_lower)
            if match:
                amount = float(match.group(1))
                break
        
        # Detect currency
        for curr, keywords in self.currency_patterns.items():
            for keyword in keywords:
                if keyword in text_lower:
                    currency = curr
                    break
        
        return amount, currency
    
    def extract_merchant_and_category(self, text: str) -> Tuple[str, str]:
        """Extract merchant and category from text"""
        text_lower = text.lower()
        
        for category, merchants in self.merchant_categories.items():
            for merchant in merchants:
                if merchant in text_lower:
                    # Extract the actual merchant name from text
                    merchant_name = self._extract_merchant_name(text, merchant)
                    return merchant_name, category
        
        return '', 'Other'
    
    def _extract_merchant_name(self, text: str, keyword: str) -> str:
        """Extract actual merchant name from text"""
        # Common merchant name mappings
        merchant_mapping = {
            'mcdonald': "McDonald's",
            'kfc': 'KFC',
            'starbucks': 'Starbucks',
            'grab': 'Grab',
            'ntuc': 'NTUC',
            'ya kun': 'Ya Kun',
            'golden village': 'Golden Village',
            '麦当劳': "McDonald's",
            '星巴克': 'Starbucks',
            '亚坤': 'Ya Kun'
        }
        
        return merchant_mapping.get(keyword.lower(), keyword.title())
    
    def extract_expense_info(self, recognized_text: str, confidence: float = 0.95) -> Dict:
        """Extract complete expense information from recognized text"""
        
        # Extract amount and currency
        amount, currency = self.extract_amount_and_currency(recognized_text)
        
        # Extract merchant and category
        merchant, category = self.extract_merchant_and_category(recognized_text)
        
        # Generate expense record
        expense_info = {
            'amount': amount or 0.0,
            'currency': currency,
            'category': category,
            'merchant': merchant,
            'description': recognized_text,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'time': datetime.now().strftime('%H:%M:%S'),
            'confidence': confidence,
            'raw_text': recognized_text
        }
        
        logger.info(f"Extracted expense info: {expense_info}")
        return expense_info

class VoiceProcessor:
    """Main voice processing service"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or "sk-8fd0324578b24b558e520cb94696267a"
        self.extractor = ExpenseInfoExtractor()
        
        if dashscope:
            dashscope.api_key = self.api_key
            logger.info("DashScope SDK initialized")
        else:
            logger.warning("DashScope SDK not available")
    
    def process_audio_file(self, audio_file_path: str) -> Dict:
        """Process audio file and extract expense information"""
        
        if not dashscope:
            # Fallback: mock processing for development
            return self._mock_processing(audio_file_path)
        
        try:
            # Create callback handler
            callback = VoiceProcessingCallback()
            
            # Create recognizer
            recognizer = TranslationRecognizerChat(
                model="gummy-chat-v1",
                format="wav",  # Assume WAV format for now
                sample_rate=16000,
                transcription_enabled=True,
                translation_enabled=False,
                callback=callback,
            )
            
            # Start recognition
            recognizer.start()
            logger.info(f"Processing audio file: {audio_file_path}")
            
            # Read and send audio data
            with open(audio_file_path, 'rb') as f:
                chunk_size = 12800  # 12.8KB chunks
                while True:
                    audio_data = f.read(chunk_size)
                    if not audio_data:
                        break
                    
                    if not recognizer.send_audio_frame(audio_data):
                        logger.info("Sentence end detected")
                        break
                    
                    time.sleep(0.1)  # Simulate real-time streaming
            
            recognizer.stop()
            
            # Wait for completion
            timeout = 30
            start_time = time.time()
            while not callback.is_complete and not callback.error_message:
                if time.time() - start_time > timeout:
                    logger.error("Recognition timeout")
                    break
                time.sleep(0.1)
            
            if callback.error_message:
                logger.error(f"Recognition failed: {callback.error_message}")
                return {'error': callback.error_message}
            
            if not callback.final_text:
                logger.warning("No recognition result")
                return {'error': 'No speech detected'}
            
            # Extract expense information
            expense_info = self.extractor.extract_expense_info(
                callback.final_text, 
                callback.confidence
            )
            
            return {
                'status': 'success',
                'recognized_text': callback.final_text,
                'expense_info': expense_info
            }
            
        except Exception as e:
            logger.error(f"Audio processing error: {e}")
            return {'error': str(e)}
    
    def _mock_processing(self, audio_file_path: str) -> Dict:
        """Mock processing for development when DashScope is not available"""
        logger.info(f"Mock processing audio file: {audio_file_path}")
        
        # Simulate processing delay
        time.sleep(1)
        
        # Mock recognition result
        mock_text = "I spent 15 dollars at McDonald's for lunch"
        expense_info = self.extractor.extract_expense_info(mock_text, 0.90)
        
        return {
            'status': 'success',
            'recognized_text': mock_text,
            'expense_info': expense_info
        }

# Global processor instance
voice_processor = VoiceProcessor()

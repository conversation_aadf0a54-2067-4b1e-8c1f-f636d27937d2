#!/usr/bin/env python3
"""
AWS RDS连接测试脚本
"""

import pymysql
import sys

# AWS RDS配置
RDS_CONFIG = {
    'host': 'db-talktab.cluwoksc8z3h.ap-southeast-1.rds.amazonaws.com',
    'port': 3306,
    'user': 'admin',
    'password': 'Flzx3000c!',
}

def test_rds_connection():
    """测试AWS RDS连接"""
    print("🧪 测试AWS RDS连接")
    print("=" * 30)
    print(f"🌐 Endpoint: {RDS_CONFIG['host']}")
    print(f"🔌 Port: {RDS_CONFIG['port']}")
    print(f"👤 User: {RDS_CONFIG['user']}")
    
    try:
        connection = pymysql.connect(
            host=RDS_CONFIG['host'],
            port=RDS_CONFIG['port'],
            user=RDS_CONFIG['user'],
            password=RDS_CONFIG['password'],
            charset='utf8mb4',
            connect_timeout=30
        )
        
        print("✅ 连接成功!")
        
        with connection.cursor() as cursor:
            # 检查MySQL版本
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"🔢 MySQL版本: {version[0]}")
            
            # 检查时区
            cursor.execute("SELECT @@time_zone, NOW()")
            timezone_info = cursor.fetchone()
            print(f"🕐 时区: {timezone_info[0]}")
            print(f"⏰ 当前时间: {timezone_info[1]}")
            
            # 检查字符集
            cursor.execute("SHOW VARIABLES LIKE 'character_set_server'")
            charset = cursor.fetchone()
            print(f"🔤 字符集: {charset[1]}")
            
            # 检查现有数据库
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print(f"📊 现有数据库: {[db[0] for db in databases]}")
            
            # 检查连接数
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            connections = cursor.fetchone()
            print(f"🔗 当前连接数: {connections[1]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n💡 可能的原因:")
        print("1. RDS实例未运行")
        print("2. 安全组未开放3306端口")
        print("3. 网络连接问题")
        print("4. 用户名或密码错误")
        return False

def main():
    if test_rds_connection():
        print("\n🎉 RDS连接测试成功!")
        print("📋 下一步: 运行 python setup_aws_rds.py 进行完整配置")
    else:
        print("\n💥 RDS连接测试失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()

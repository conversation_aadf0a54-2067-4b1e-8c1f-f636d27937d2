#!/usr/bin/env python3
"""
Test script for multiple expenses parsing functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.voice_processor import VoiceProcessor

def test_multiple_expenses_parsing():
    processor = VoiceProcessor()
    
    test_cases = [
        # Test case 1: The actual user input
        "我昨天花了10块钱吃晚饭，今天早上花了5块钱打车",
        
        # Test case 2: Other multiple expense patterns
        "我买了咖啡花了15块钱，然后买了面包花了8块钱",
        "昨天打车花了20元，今天吃饭花了30元",
        "I spent 10 dollars on coffee, then paid 15 dollars for lunch",
        "买了水果5块钱，买了蔬菜12块钱",
    ]
    
    print("Testing multiple expenses parsing:")
    print("=" * 60)
    
    for i, text in enumerate(test_cases, 1):
        print(f"\nTest {i}: {text}")
        print("-" * 40)
        
        # Test the multiple expenses parsing
        expense_list = processor._parse_multiple_expenses(text)
        
        print(f"Found {len(expense_list)} expense(s):")
        for j, expense in enumerate(expense_list, 1):
            print(f"  Expense {j}:")
            print(f"    Amount: {expense.get('amount', 'N/A')}")
            print(f"    Currency: {expense.get('currency', 'N/A')}")
            print(f"    Category: {expense.get('category', 'N/A')}")
            print(f"    Merchant: {expense.get('merchant', 'N/A')}")
            print(f"    Description: {expense.get('description', 'N/A')}")
            print(f"    Raw text: {expense.get('raw_text', 'N/A')}")
        
        # Check if multiple expenses were correctly detected
        if len(expense_list) > 1:
            print("  ✅ Multiple expenses detected correctly")
        else:
            print("  ⚠️  Only single expense detected")

if __name__ == "__main__":
    test_multiple_expenses_parsing()

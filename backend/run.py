#!/usr/bin/env python3
"""
Voice Accounting Backend - Main Entry Point
"""

import os
from main import app, db

def create_tables():
    """Create database tables if they don't exist"""
    with app.app_context():
        db.create_all()
        print("✅ Database tables ready!")

if __name__ == '__main__':
    # Create tables
    create_tables()

    # Get configuration from environment
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    host = os.getenv('FLASK_HOST', '127.0.0.1')
    port = int(os.getenv('FLASK_PORT', 5000))

    config_name = os.environ.get('FLASK_ENV', 'development')

    print(f"🚀 Starting Voice Accounting Backend ({config_name})")
    print(f"📍 Server: http://{host}:{port}")
    print(f"🔍 Debug mode: {debug}")
    print("=" * 50)

    # Run the Flask development server
    app.run(
        host=host,
        port=port,
        debug=debug
    )

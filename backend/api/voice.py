"""
Voice Processing API Endpoints
"""

import os
import uuid
import logging
from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename
from datetime import datetime

# Import voice processor service
try:
    from services.voice_processor import voice_processor
except ImportError as e:
    logging.error(f"Failed to import voice processor: {e}")
    voice_processor = None

voice_bp = Blueprint('voice', __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

ALLOWED_EXTENSIONS = {'wav', 'mp3', 'm4a', 'aac', 'ogg'}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@voice_bp.route('/upload', methods=['POST'])
def upload_voice():
    """Upload voice recording for processing"""
    
    try:
        # Check if file is present
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400
        
        file = request.files['audio']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Check file type
        if not allowed_file(file.filename):
            return jsonify({
                'error': 'Invalid file type. Allowed types: wav, mp3, m4a, aac, ogg'
            }), 400
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        original_filename = secure_filename(file.filename)
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        filename = f"{file_id}.{file_extension}"
        
        # Create upload directory if it doesn't exist
        upload_folder = os.getenv('UPLOAD_FOLDER', '/tmp/voice_uploads')
        os.makedirs(upload_folder, exist_ok=True)
        filepath = os.path.join(upload_folder, filename)

        # Save file temporarily
        file.save(filepath)
        logger.info(f"Saved audio file: {filepath}")

        # Process with Qwen AI
        if voice_processor:
            processing_result = voice_processor.process_audio_file(filepath)

            if 'error' in processing_result:
                # Clean up file on error
                try:
                    os.remove(filepath)
                except:
                    pass
                return jsonify({'error': processing_result['error']}), 500

            # Extract expense info from processing result
            expense_info = processing_result.get('expense_info', {})
            recognized_text = processing_result.get('recognized_text', '')

            response_data = {
                'amount': expense_info.get('amount', 0.0),
                'currency': expense_info.get('currency', 'SGD'),
                'category': expense_info.get('category', 'Other'),
                'merchant': expense_info.get('merchant', ''),
                'description': expense_info.get('description', recognized_text),
                'date': expense_info.get('date', datetime.now().strftime('%Y-%m-%d')),
                'time': expense_info.get('time', datetime.now().strftime('%H:%M:%S')),
                'confidence': expense_info.get('confidence', 0.0),
                'recognized_text': recognized_text,
                'raw_text': expense_info.get('raw_text', recognized_text)
            }
        else:
            # Fallback mock response if voice processor is not available
            logger.warning("Voice processor not available, using mock response")
            response_data = {
                'amount': 15.50,
                'currency': 'SGD',
                'category': 'Food & Dining',
                'merchant': "McDonald's",
                'description': 'Mock voice recognition result',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'time': datetime.now().strftime('%H:%M:%S'),
                'confidence': 0.90,
                'recognized_text': 'Mock voice recognition result',
                'raw_text': 'Mock voice recognition result'
            }
        
        # Clean up temporary file
        try:
            os.remove(filepath)
            logger.info(f"Cleaned up temporary file: {filepath}")
        except Exception as e:
            logger.warning(f"Failed to clean up file {filepath}: {e}")

        return jsonify({
            'id': file_id,
            'status': 'processed',
            'result': response_data
        })
        
    except Exception as e:
        logger.error(f"Voice upload error: {e}")
        # Clean up file if it exists
        try:
            if 'filepath' in locals():
                os.remove(filepath)
        except:
            pass
        return jsonify({'error': str(e)}), 500

@voice_bp.route('/status/<processing_id>')
def get_processing_status(processing_id):
    """Get processing status for a voice upload"""
    
    try:
        # TODO: Implement actual status checking
        # For now, return a mock response
        return jsonify({
            'id': processing_id,
            'status': 'completed',
            'progress': 100,
            'result': {
                'amount': 25.80,
                'currency': 'SGD',
                'category': 'Transportation',
                'description': 'Grab ride to office',
                'date': datetime.utcnow().isoformat(),
                'confidence': 0.92
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

"""
Configuration settings for Voice Accounting Flask Backend
"""
import os
import pytz
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# 新加坡时区
SINGAPORE_TZ = pytz.timezone('Asia/Singapore')

class Config:
    """Base configuration"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

    # 时区配置
    TIMEZONE = 'Asia/Singapore'

    @staticmethod
    def get_singapore_time():
        """获取新加坡当前时间"""
        return datetime.now(SINGAPORE_TZ)

    @staticmethod
    def utc_to_singapore(utc_dt):
        """将UTC时间转换为新加坡时间"""
        if utc_dt.tzinfo is None:
            utc_dt = pytz.utc.localize(utc_dt)
        return utc_dt.astimezone(SINGAPORE_TZ)

    @staticmethod
    def singapore_to_utc(sg_dt):
        """将新加坡时间转换为UTC时间"""
        if sg_dt.tzinfo is None:
            sg_dt = SINGAPORE_TZ.localize(sg_dt)
        return sg_dt.astimezone(pytz.utc)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///voice_accounting_dev.db'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    # MySQL/MariaDB connection string
    # Format: mysql+pymysql://username:password@host:port/database_name
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://voice_user:password@localhost:3306/voice_accounting'

    # MySQL连接池配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': int(os.environ.get('DB_POOL_SIZE', 10)),
        'pool_timeout': int(os.environ.get('DB_POOL_TIMEOUT', 30)),
        'pool_recycle': int(os.environ.get('DB_POOL_RECYCLE', 3600)),
        'pool_pre_ping': True,  # 连接前测试连接是否有效
        'echo': False,  # 生产环境不输出SQL日志
        # MySQL 8.0兼容性配置
        'connect_args': {
            'charset': 'utf8mb4'
        }
    }

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

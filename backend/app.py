#!/usr/bin/env python3
"""
Voice Accounting Flask Backend
A RESTful API server for the Voice Accounting iOS app with database support
"""

from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from datetime import datetime, date
import os
import json
from werkzeug.utils import secure_filename
import uuid

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'

# Database configuration
# For development: SQLite
# For production: MySQL/MariaDB
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///voice_accounting.db')
app.config['SQLALCHEMY_DATABASE_URI'] = DATABASE_URL
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# File upload configuration
UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize extensions
db = SQLAlchemy(app)
CORS(app)  # Enable CORS for iOS app

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Database Models
class User(db.Model):
    """User model for multi-user support"""
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    currency = db.Column(db.String(3), default='USD')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    expenses = db.relationship('Expense', backref='user', lazy=True, cascade='all, delete-orphan')
    categories = db.relationship('Category', backref='user', lazy=True, cascade='all, delete-orphan')

class Category(db.Model):
    """Expense categories"""
    __tablename__ = 'categories'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    icon = db.Column(db.String(10), default='📝')
    color = db.Column(db.String(20), default='gray')
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    expenses = db.relationship('Expense', backref='category_obj', lazy=True)

class Expense(db.Model):
    """Expense records"""
    __tablename__ = 'expenses'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    amount = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text, nullable=False)
    category_id = db.Column(db.String(36), db.ForeignKey('categories.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    date = db.Column(db.Date, default=date.today)
    time = db.Column(db.String(10), nullable=False)  # HH:MM format
    voice_file_path = db.Column(db.String(255), nullable=True)  # Path to voice recording
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Helper Functions
def serialize_expense(expense):
    """Convert expense object to dictionary"""
    return {
        'id': expense.id,
        'amount': expense.amount,
        'description': expense.description,
        'category': expense.category_obj.name,
        'category_id': expense.category_id,
        'icon': expense.category_obj.icon,
        'color': expense.category_obj.color,
        'date': expense.date.isoformat(),
        'time': expense.time,
        'created_at': expense.created_at.isoformat(),
        'updated_at': expense.updated_at.isoformat()
    }

def serialize_category(category):
    """Convert category object to dictionary"""
    return {
        'id': category.id,
        'name': category.name,
        'icon': category.icon,
        'color': category.color,
        'created_at': category.created_at.isoformat()
    }

def serialize_user(user):
    """Convert user object to dictionary"""
    return {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'currency': user.currency,
        'created_at': user.created_at.isoformat()
    }

# API Routes
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Voice Accounting API is running',
        'timestamp': datetime.utcnow().isoformat()
    })

@app.route('/api/users', methods=['POST'])
def create_user():
    """Create a new user"""
    data = request.get_json()
    
    if not data or 'username' not in data:
        return jsonify({'error': 'Username is required'}), 400
    
    # Check if user already exists
    existing_user = User.query.filter_by(username=data['username']).first()
    if existing_user:
        return jsonify({'error': 'Username already exists'}), 409
    
    # Create new user
    user = User(
        username=data['username'],
        email=data.get('email'),
        currency=data.get('currency', 'USD')
    )
    
    try:
        db.session.add(user)
        db.session.commit()
        
        # Create default categories for new user
        create_default_categories(user.id)
        
        return jsonify({
            'message': 'User created successfully',
            'user': serialize_user(user)
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<user_id>', methods=['GET'])
def get_user(user_id):
    """Get user information"""
    user = User.query.get_or_404(user_id)
    return jsonify({'user': serialize_user(user)})

@app.route('/api/users/<user_id>', methods=['PUT'])
def update_user(user_id):
    """Update user information"""
    user = User.query.get_or_404(user_id)
    data = request.get_json()
    
    if 'currency' in data:
        user.currency = data['currency']
    if 'email' in data:
        user.email = data['email']
    
    try:
        db.session.commit()
        return jsonify({
            'message': 'User updated successfully',
            'user': serialize_user(user)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def create_default_categories(user_id):
    """Create default expense categories for a new user"""
    default_categories = [
        {'name': 'Food & Dining', 'icon': '🍽️', 'color': 'orange'},
        {'name': 'Transportation', 'icon': '🚗', 'color': 'blue'},
        {'name': 'Shopping', 'icon': '🛍️', 'color': 'green'},
        {'name': 'Entertainment', 'icon': '🎬', 'color': 'purple'},
        {'name': 'Healthcare', 'icon': '🏥', 'color': 'red'},
        {'name': 'Bills & Utilities', 'icon': '📄', 'color': 'yellow'},
        {'name': 'Education', 'icon': '📚', 'color': 'indigo'},
        {'name': 'Others', 'icon': '📝', 'color': 'gray'}
    ]
    
    for cat_data in default_categories:
        category = Category(
            name=cat_data['name'],
            icon=cat_data['icon'],
            color=cat_data['color'],
            user_id=user_id
        )
        db.session.add(category)
    
    db.session.commit()

if __name__ == '__main__':
    # Create database tables
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    
    # Run the app
    app.run(debug=True, host='0.0.0.0', port=5000)

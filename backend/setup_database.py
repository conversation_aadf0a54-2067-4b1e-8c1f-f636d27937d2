#!/usr/bin/env python3
"""
Database setup script for Voice Accounting Backend
This script creates the database and tables for MySQL/MariaDB
"""

import os
import sys
from sqlalchemy import create_engine, text
from config import config
from main import app, db

def create_mysql_database():
    """Create MySQL/MariaDB database if it doesn't exist"""
    
    # Get database configuration
    config_name = os.environ.get('FLASK_ENV', 'development')
    app_config = config[config_name]
    
    database_url = app_config.SQLALCHEMY_DATABASE_URI
    
    if not database_url.startswith('mysql'):
        print("This script is for MySQL/MariaDB setup only.")
        print(f"Current database URL: {database_url}")
        return False
    
    # Parse database URL to get connection details
    # Format: mysql+pymysql://username:password@host:port/database_name
    try:
        from urllib.parse import urlparse
        parsed = urlparse(database_url)
        
        host = parsed.hostname
        port = parsed.port or 3306
        username = parsed.username
        password = parsed.password
        database_name = parsed.path.lstrip('/')
        
        print(f"Connecting to MySQL server at {host}:{port}")
        print(f"Database: {database_name}")
        print(f"Username: {username}")
        
        # Connect to MySQL server (without specifying database)
        server_url = f"mysql+pymysql://{username}:{password}@{host}:{port}"
        engine = create_engine(server_url)
        
        # Create database if it doesn't exist
        with engine.connect() as conn:
            conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {database_name}"))
            conn.commit()
            print(f"✅ Database '{database_name}' created successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False

def create_tables():
    """Create all database tables"""
    try:
        with app.app_context():
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Print table information
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📋 Created tables: {', '.join(tables)}")
            
        return True
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def create_sample_data():
    """Create sample user and data for testing"""
    try:
        with app.app_context():
            from main import User, Category, Expense
            from datetime import date, datetime
            
            # Check if sample user already exists
            sample_user = User.query.filter_by(username='demo_user').first()
            if sample_user:
                print("📝 Sample user already exists")
                return True
            
            # Create sample user
            user = User(
                username='demo_user',
                email='<EMAIL>',
                currency='USD'
            )
            db.session.add(user)
            db.session.flush()  # Get the user ID
            
            # Create default categories
            categories_data = [
                {'name': 'Food & Dining', 'icon': '🍽️', 'color': 'orange'},
                {'name': 'Transportation', 'icon': '🚗', 'color': 'blue'},
                {'name': 'Shopping', 'icon': '🛍️', 'color': 'green'},
                {'name': 'Entertainment', 'icon': '🎬', 'color': 'purple'},
                {'name': 'Healthcare', 'icon': '🏥', 'color': 'red'},
                {'name': 'Bills & Utilities', 'icon': '📄', 'color': 'yellow'},
                {'name': 'Education', 'icon': '📚', 'color': 'indigo'},
                {'name': 'Others', 'icon': '📝', 'color': 'gray'}
            ]
            
            categories = []
            for cat_data in categories_data:
                category = Category(
                    name=cat_data['name'],
                    icon=cat_data['icon'],
                    color=cat_data['color'],
                    user_id=user.id
                )
                db.session.add(category)
                categories.append(category)
            
            db.session.flush()  # Get category IDs
            
            # Create sample expenses
            sample_expenses = [
                {
                    'amount': 45.00,
                    'description': 'Lunch at Italian restaurant',
                    'category': categories[0],  # Food & Dining
                    'time': '12:30',
                    'date': date.today()
                },
                {
                    'amount': 8.00,
                    'description': 'Morning coffee',
                    'category': categories[0],  # Food & Dining
                    'time': '10:15',
                    'date': date.today()
                },
                {
                    'amount': 25.00,
                    'description': 'Subway monthly pass',
                    'category': categories[1],  # Transportation
                    'time': '09:00',
                    'date': date.today()
                }
            ]
            
            for exp_data in sample_expenses:
                expense = Expense(
                    amount=exp_data['amount'],
                    description=exp_data['description'],
                    category_id=exp_data['category'].id,
                    user_id=user.id,
                    time=exp_data['time'],
                    date=exp_data['date']
                )
                db.session.add(expense)
            
            db.session.commit()
            print("✅ Sample data created successfully!")
            print(f"👤 Demo user ID: {user.id}")
            print(f"📊 Created {len(categories)} categories and {len(sample_expenses)} sample expenses")
            
        return True
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.session.rollback()
        return False

def main():
    """Main setup function"""
    print("🚀 Voice Accounting Database Setup")
    print("=" * 40)
    
    # Set configuration
    config_name = os.environ.get('FLASK_ENV', 'development')
    print(f"📝 Environment: {config_name}")
    
    app.config.from_object(config[config_name])
    
    # Step 1: Create database (MySQL/MariaDB only)
    if app.config['SQLALCHEMY_DATABASE_URI'].startswith('mysql'):
        print("\n1️⃣ Creating MySQL/MariaDB database...")
        if not create_mysql_database():
            sys.exit(1)
    else:
        print("\n1️⃣ Using SQLite database (no setup required)")
    
    # Step 2: Create tables
    print("\n2️⃣ Creating database tables...")
    if not create_tables():
        sys.exit(1)
    
    # Step 3: Create sample data
    print("\n3️⃣ Creating sample data...")
    if not create_sample_data():
        sys.exit(1)
    
    print("\n🎉 Database setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Start the Flask server: python app.py")
    print("2. Test the API: curl http://localhost:5000/api/health")
    print("3. View sample user: curl http://localhost:5000/api/users/<user_id>")

if __name__ == '__main__':
    main()

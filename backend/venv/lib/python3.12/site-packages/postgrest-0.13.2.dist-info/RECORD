postgrest-0.13.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
postgrest-0.13.2.dist-info/LICENSE,sha256=ht_XqL11RMhYK9-Ip9-6hpflU0qtpFOUO9VIdua-mVs,1077
postgrest-0.13.2.dist-info/METADATA,sha256=iEmr2kzBUgt9xSgIlMHJa74E1qaukJZuNyRknvVLilY,5127
postgrest-0.13.2.dist-info/RECORD,,
postgrest-0.13.2.dist-info/WHEEL,sha256=FMvqSimYX_P7y0a7UY-_Mc83r5zkBZsCYPm7Lr0Bsq4,88
postgrest/__init__.py,sha256=-SVOm-S73MuprPmQ_l58UovjkZ0Ny5rOnPn9<PERSON><PERSON><PERSON>is,941
postgrest/__pycache__/__init__.cpython-312.pyc,,
postgrest/__pycache__/base_client.cpython-312.pyc,,
postgrest/__pycache__/base_request_builder.cpython-312.pyc,,
postgrest/__pycache__/constants.cpython-312.pyc,,
postgrest/__pycache__/deprecated_client.cpython-312.pyc,,
postgrest/__pycache__/deprecated_get_request_builder.cpython-312.pyc,,
postgrest/__pycache__/exceptions.cpython-312.pyc,,
postgrest/__pycache__/types.cpython-312.pyc,,
postgrest/__pycache__/utils.cpython-312.pyc,,
postgrest/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_async/__pycache__/__init__.cpython-312.pyc,,
postgrest/_async/__pycache__/client.cpython-312.pyc,,
postgrest/_async/__pycache__/request_builder.cpython-312.pyc,,
postgrest/_async/client.py,sha256=PIwi4XPxZ3_uw0BgKqlsCHQrVKEsAg850a-85twDwVk,3263
postgrest/_async/request_builder.py,sha256=qjCQAZDgTgY-vFmHmeyyYnK5WMwVmGTug8ALn2TjMzU,13184
postgrest/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_sync/__pycache__/__init__.cpython-312.pyc,,
postgrest/_sync/__pycache__/client.cpython-312.pyc,,
postgrest/_sync/__pycache__/request_builder.cpython-312.pyc,,
postgrest/_sync/client.py,sha256=nn7BYFzuewB1erRgB_PGafWrKu6OZfJr3fi1-IEPK1U,3217
postgrest/_sync/request_builder.py,sha256=aT0by1QDVPIjBnqDcwC5OHlnppStGRaF5E-z71SM0sY,13108
postgrest/base_client.py,sha256=E3QHWwENwzawZVjYma0D8yxacyW5u9hcM_pIBZZnINY,1918
postgrest/base_request_builder.py,sha256=jW0uY77shlbrbMvVNkoMV3sMpNwqTpu6gpS5iEhgZmI,17108
postgrest/constants.py,sha256=9jR_zaLyfEvNI0WrL2vRs4NsjkGKomMk2sI_tjmcb3k,151
postgrest/deprecated_client.py,sha256=jcGEceLU_t_Fk57cZHijRLf3N2T10HGMTGDqAFS6IMg,409
postgrest/deprecated_get_request_builder.py,sha256=dWSHNtw6CXcp4HfjqkcSTaOeKeI007_wtMrHbDNODLU,422
postgrest/exceptions.py,sha256=T4ORME29C_CyIJUCWlDXoS3qSyv7LxMov1xLyMGvfho,1510
postgrest/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
postgrest/types.py,sha256=WjqZnDToAmbSTtxXtED17p-G2zCc-LtXNcc67umfKK0,874
postgrest/utils.py,sha256=_9qLBJvOImBRkln1dJk6k236CcmcYOajhJMS-b5czmw,1152

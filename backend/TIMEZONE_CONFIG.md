# 新加坡时区配置指南

## 🕐 概述

Voice Accounting应用现已配置为使用新加坡时区（Asia/Singapore, UTC+8）。所有API返回的时间戳都是新加坡本地时间。

## ⚙️ 配置详情

### 1. 时区设置
- **应用时区**: Asia/Singapore (UTC+8)
- **数据库存储**: UTC时间（内部转换）
- **API返回**: 新加坡本地时间

### 2. 时间处理流程

```
用户输入 → 新加坡时间 → 转换为UTC → 存储到数据库
数据库 → UTC时间 → 转换为新加坡时间 → API返回
```

### 3. 关键函数

#### 时区转换函数
```python
def get_singapore_now():
    """获取新加坡当前时间"""
    return datetime.now(SINGAPORE_TZ)

def utc_to_singapore(utc_dt):
    """将UTC时间转换为新加坡时间"""
    if utc_dt.tzinfo is None:
        utc_dt = pytz.utc.localize(utc_dt)
    return utc_dt.astimezone(SINGAPORE_TZ)

def singapore_to_utc(sg_dt):
    """将新加坡时间转换为UTC时间"""
    if sg_dt.tzinfo is None:
        sg_dt = SINGAPORE_TZ.localize(sg_dt)
    return sg_dt.astimezone(pytz.utc)

def format_singapore_time(dt):
    """格式化新加坡时间为ISO字符串"""
    if dt.tzinfo is None:
        dt = pytz.utc.localize(dt)
    sg_time = dt.astimezone(SINGAPORE_TZ)
    return sg_time.isoformat()
```

## 📊 API时间格式

### Health Check API
```json
{
  "status": "healthy",
  "message": "Voice Accounting API is running",
  "timestamp": "2025-07-13T22:50:50.968302+08:00",
  "timezone": "Asia/Singapore (UTC+8)",
  "server_time": "2025-07-13 22:50:50 +08"
}
```

### 用户创建API
```json
{
  "message": "User created successfully",
  "user": {
    "id": "cd33002a-47d5-4e58-a3cb-443bf45aeafa",
    "username": "singapore_test_user",
    "email": "<EMAIL>",
    "currency": "SGD",
    "created_at": "2025-07-13T22:51:47+08:00"
  }
}
```

### 支出记录API
```json
{
  "id": "expense-id",
  "amount": 25.50,
  "description": "Lunch",
  "category": "Food & Dining",
  "date": "2025-07-13",
  "time": "12:30",
  "created_at": "2025-07-13T22:51:47+08:00",
  "updated_at": "2025-07-13T22:51:47+08:00"
}
```

## 🔧 数据库配置

### 数据库时区设置
```sql
-- 检查MySQL时区设置
SELECT @@global.time_zone, @@session.time_zone;

-- 设置会话时区为UTC（推荐）
SET time_zone = '+00:00';
```

### 数据库字段
- `created_at`: DATETIME (存储UTC时间)
- `updated_at`: DATETIME (存储UTC时间)
- `date`: DATE (日期，不含时区信息)
- `time`: VARCHAR(10) (时间字符串，如"12:30")

## 📱 iOS应用集成

### NetworkService配置
```swift
// 更新后端URL端口
private let baseURL = "http://127.0.0.1:5001/api"
```

### 时间处理
iOS应用接收到的所有时间戳都是新加坡时间，可以直接显示给用户。

## 🧪 测试时区功能

### 1. 测试Health Check
```bash
curl http://127.0.0.1:5001/api/health
```

### 2. 创建测试用户
```bash
curl -X POST http://127.0.0.1:5001/api/users \
  -H "Content-Type: application/json" \
  -d '{"username": "test_user", "email": "<EMAIL>", "currency": "SGD"}'
```

### 3. 验证时间戳
检查返回的`created_at`字段是否包含`+08:00`时区标识。

## 🌍 其他时区支持

如果需要支持其他时区，可以：

### 1. 添加用户时区字段
```python
class User(db.Model):
    timezone = db.Column(db.String(50), default='Asia/Singapore')
```

### 2. 动态时区转换
```python
def get_user_timezone(user_id):
    user = User.query.get(user_id)
    return pytz.timezone(user.timezone)

def format_user_time(dt, user_id):
    user_tz = get_user_timezone(user_id)
    if dt.tzinfo is None:
        dt = pytz.utc.localize(dt)
    user_time = dt.astimezone(user_tz)
    return user_time.isoformat()
```

## 📋 注意事项

### 1. 夏令时
新加坡不使用夏令时，全年UTC+8，无需特殊处理。

### 2. 数据迁移
如果从其他时区迁移数据，需要：
- 确认原始数据的时区
- 转换为UTC后存储
- 更新API返回格式

### 3. 前端显示
- 所有时间戳都包含时区信息
- 前端可以直接显示，无需额外转换
- 建议显示格式：`2025-07-13 22:51 SGT`

## 🔍 故障排除

### 常见问题

1. **时间戳不正确**
   - 检查服务器系统时间
   - 验证pytz库安装
   - 确认时区配置

2. **数据库时间错误**
   - 检查MySQL时区设置
   - 验证连接字符串
   - 确认数据存储格式

3. **API返回UTC时间**
   - 检查序列化函数
   - 验证时区转换逻辑
   - 确认配置加载

### 调试命令
```bash
# 检查系统时区
date
timedatectl

# 检查Python时区
python3 -c "import datetime, pytz; print(datetime.datetime.now(pytz.timezone('Asia/Singapore')))"

# 测试API时区
curl http://127.0.0.1:5001/api/health | jq '.timestamp'
```

## ✅ 验证清单

- [ ] Health API返回新加坡时间
- [ ] 用户创建时间正确
- [ ] 支出记录时间正确
- [ ] 时区标识显示+08:00
- [ ] iOS应用连接正常
- [ ] 数据库存储UTC时间
- [ ] API返回本地时间

配置完成后，整个系统将统一使用新加坡时区！🇸🇬
